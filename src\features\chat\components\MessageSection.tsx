import { useEffect, useRef, useState } from "react";
import { Input } from "@/components/ui/input";
import { timeFormatter } from "@/lib/utils";
import GoBackIcon from "@/assets/go-back.svg";
import SendIcon from "@/assets/send.svg";
import Buttonwithicon from "@/components/ui/buttonwithicon";
import EmptyConversation from "./EmptyConversation";

function MessageSection({
  name,
  description,
  handleHideMessageSection,
}: {
  name: string;
  description: string;
  handleHideMessageSection: () => void;
}) {
  const user = localStorage.getItem("nurtureUser");
  const [message, setMessage] = useState("");
  const lastMessageRef = useRef(null);
  useEffect(() => {
    // Scroll to the last message
    if (lastMessageRef.current) {
      (lastMessageRef.current as HTMLElement)?.scrollIntoView({
        behavior: "smooth",
      });
    }
  }, []);
  const conversations = [
    {
      id: 1,
      message:
        "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.",
      sender: "seller",
      timestamp: "2025-05-16T12:43:15.919Z",
    },
    {
      id: 2,
      message:
        "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.",
      sender: "customer",
      timestamp: "2025-05-16T12:43:15.919Z",
    },
    {
      id: 3,
      message: "Hello",
      sender: "seller",
      timestamp: "2025-06-02T10:25:47.123Z",
    },
    {
      id: 4,
      message: "I have a doubt",
      sender: "customer",
      timestamp: "2025-05-16T12:43:15.919Z",
    },
  ];

  const handleSendMessage = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!message.trim()) return;
    // Send message
    setMessage("");
  };
  return (
    <div className="flex flex-col w-full h-full">
      {/* Header */}

      {conversations.length ? (
        <>
          <div className="flex flex-shrink-0 p-4 border-b gap-x-3 md:gap-0 border-nueutral-40">
            <img
              onClick={handleHideMessageSection}
              src={GoBackIcon}
              alt="go back"
              className="w-4 h-4 my-auto cursor-pointer md:hidden"
            />
            <div>
              <h1>{name}</h1>
              <h5 className="text-sm text-neutral-300">{description}</h5>
            </div>
          </div>

          <div className="flex-1 min-h-0 p-5 overflow-y-auto scrollbar-hide">
            <div className="flex flex-col">
              {conversations.map((conversation, index) => (
                <div
                  ref={
                    index === conversations.length - 1 ? lastMessageRef : null
                  }
                  className={`my-3 p-3 max-w-[80%] md:max-w-[49%] border ${
                    conversation.sender === user
                      ? "bg-coral text-white self-end rounded-tr-none rounded-xl"
                      : "self-start rounded-tl-none rounded-xl border-neutral-50"
                  }`}
                >
                  <p className="break-words">{conversation.message}</p>
                  <p
                    className={`${conversation.sender === user ? "text-tints-60" : "text-neutral-300"} text-sm text-end mt-1`}
                  >
                    {timeFormatter(conversation.timestamp)}
                  </p>
                </div>
              ))}
            </div>
          </div>

          <form
            onSubmit={handleSendMessage}
            className="flex flex-shrink-0 p-4 border-t gap-x-2 border-neutral-40"
          >
            <Input
              type="text"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Type message here..."
              className="w-full p-2 border rounded-md grow border-neutral-40"
            />
            <Buttonwithicon
              disabled={message.trim().length === 0}
              icon={SendIcon}
              variant="button"
            />
          </form>
        </>
      ) : (
        <EmptyConversation handleHideMessageSection={handleHideMessageSection} />
      )}
    </div>
  );
}

export default MessageSection;
