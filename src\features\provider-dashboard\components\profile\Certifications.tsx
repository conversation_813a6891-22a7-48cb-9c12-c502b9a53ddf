import { But<PERSON> } from "@/components/ui/button";
import { Plus, Trash2 } from "lucide-react";
import { useState } from "react";

interface Certification {
  id: string;
  name: string;
  file: string;
}

interface CertificationsProps {
  certifications: Certification[];
}

function Certifications({ certifications = [] }: CertificationsProps) {
  const [isAdding, setIsAdding] = useState(false);
  console.log(isAdding)

  return (
    <div className="mb-8">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-base ">Certifications</h2>
        <Button 
          variant="outline"
          onClick={() => setIsAdding(true)}
          className="flex items-center gap-2 text-orange-1 border-orange-1"
        >
          Add <Plus className="w-4 h-4" />
        </Button>
      </div>
      
      {certifications.length > 0 ? (
        <div className="space-y-3">
          {certifications.map((cert) => (
            <div 
              key={cert.id}
              className="flex justify-between items-center p-4 border border-gray-200 rounded-lg"
            >
              <span className="text-lg font-medium">{cert.name}</span>
              <Button 
                variant="ghost" 
                size="sm"
                className="text-gray-500 hover:text-red-500"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>
          ))}
        </div>
      ) : (
        <p className="text-gray-500">No certifications added yet.</p>
      )}
    </div>
  );
}

export default Certifications;
