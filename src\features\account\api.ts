import api from "@/lib/axios";
import { BasicDetailsData, CarePlanCardItem } from "./type";
import { returnOrExchangeApiPayload } from "./type";

export const fetchOrdersApi = async () => {
  const { data } = await api.get("/api/v1/order");
  return data;
};
export const fetchOrderedProductApi = async (productId: string) => {
  const { data } = await api.get(`/api/v1/order/${productId}`);
  return data;
};

export const fetchSellerRefundPolicy = async (sellerId: string) => {
  const { data } = await api.get(
    `/api/v1/users/seller/${sellerId}/refund-policy`
  );
  return data;
};

export const cancelOrderApi = async (orderId: string) => {
  const { data } = await api.patch(`/api/v1/order/${orderId}/cancel`);
  return data;
};

export const fetchUserProfileApi = async () => {
  try {
    const response = await api.get("/api/v1/users/customer/profile");
    return response.data;
  } catch (error) {
    console.error("Error fetching user profile:", error);
    throw error;
  }
};

export const updateUserProfileApi = async (payload: BasicDetailsData) => {
  try {
    const response = await api.patch("/api/v1/users/customer", payload);
    return response.data;
  } catch (error) {
    console.error("Error updating user profile:", error);
    throw error;
  }
};

export const returnOrExchangeApi = async (
  payload: returnOrExchangeApiPayload
) => {
  const formData = new FormData();
  formData.append("reasons", payload.reason.join(","));
  payload.images.forEach((image) => {
    formData.append("images", image);
  });
  const { data } = await api.patch(
    `/api/v1/order/${payload.orderId}/${payload.type}`,
    formData,
    {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }
  );
  return data;
};

export const submitReviewApi = async (payload: {
  rating: number;
  review: string;
  productId: string;
}) => {
  const { data } = await api.post(`/api/v1/review`, payload);
  return data;
};

export const fetchCarePlanApi = async (): Promise<CarePlanCardItem[]> => {
  const { data } = await api.get("/api/v1/users/customer/care-plan");
  return data?.data?.carePlan;
};
