import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Plus, Trash2 } from "lucide-react";
import { TimePicker } from "@/components/ui/time-picker";
import { useMutation } from "@tanstack/react-query";
import { setProviderAvailabilityApi, AvailabilityPayload } from "../api";
import { showToast } from "@/lib/toast";

type TimeSlot = {
  id: string;
  startTime: string;
  endTime: string;
};

type DaySchedule = {
  day: string;
  isAvailable: boolean;
  timeSlots: TimeSlot[];
};

type ProfileSetupStep3Props = {
  onNext: () => void;
  onBack: () => void;
};

export default function ProfileSetupStep3({ onNext, onBack }: ProfileSetupStep3Props) {
  const [providerId, setProviderId] = useState<string>("");

  // Get providerId from localStorage
  useEffect(() => {
    const storedProviderId = localStorage.getItem("providerId");
    if (storedProviderId) {
      console.log("Provider ID found in localStorage:", storedProviderId);
      setProviderId(storedProviderId);
    } else {
      console.error("Provider ID not found in localStorage");
      showToast("Provider ID not found. Please go back and complete the previous steps first.", "error");
    }
  }, []);

  // Initialize schedule for all days of the week
  const [schedule, setSchedule] = useState<DaySchedule[]>([
    { day: "SUN", isAvailable: false, timeSlots: [] },
    { day: "MON", isAvailable: true, timeSlots: [{ id: crypto.randomUUID(), startTime: "9:00 AM", endTime: "5:30 PM" }] },
    { day: "TUE", isAvailable: true, timeSlots: [{ id: crypto.randomUUID(), startTime: "9:00 AM", endTime: "5:30 PM" }] },
    { day: "WED", isAvailable: true, timeSlots: [{ id: crypto.randomUUID(), startTime: "9:00 AM", endTime: "5:30 PM" }] },
    { day: "THU", isAvailable: true, timeSlots: [{ id: crypto.randomUUID(), startTime: "9:00 AM", endTime: "5:30 PM" }] },
    { day: "FRI", isAvailable: true, timeSlots: [{ id: crypto.randomUUID(), startTime: "9:00 AM", endTime: "5:30 PM" }] },
    { day: "SAT", isAvailable: false, timeSlots: [] },
  ]);

  // API mutation for setting availability
  const { mutate: setAvailability, isPending } = useMutation({
    mutationFn: setProviderAvailabilityApi,
    onSuccess: (response) => {
      if (response.status === "OK") {
        // Update the status flag for availability
        localStorage.setItem("hasSetAvailability", "true");
        showToast("Availability set successfully", "success");
        onNext();
      } else {
        showToast("Failed to set availability", "error");
      }
    },
    onError: (error) => {
      console.error("Availability setting error:", error);
      showToast("Failed to set availability", "error");
    }
  });

  // Toggle day availability
  const toggleDayAvailability = (dayIndex: number) => {
    const updatedSchedule = [...schedule];
    updatedSchedule[dayIndex].isAvailable = !updatedSchedule[dayIndex].isAvailable;

    // If day is marked as unavailable, clear time slots
    if (!updatedSchedule[dayIndex].isAvailable) {
      updatedSchedule[dayIndex].timeSlots = [];
    } else if (updatedSchedule[dayIndex].timeSlots.length === 0) {
      // If day is marked as available and has no time slots, add a default one
      updatedSchedule[dayIndex].timeSlots = [
        { id: crypto.randomUUID(), startTime: "09:00 am", endTime: "05:00 pm" }
      ];
    }

    setSchedule(updatedSchedule);
  };

  // Add a new time slot for a day
  const addTimeSlot = (dayIndex: number) => {
    const updatedSchedule = [...schedule];
    updatedSchedule[dayIndex].timeSlots.push({
      id: crypto.randomUUID(),
      startTime: "9:00 AM",
      endTime: "5:30 PM"
    });
    setSchedule(updatedSchedule);
  };

  // Remove a time slot
  const removeTimeSlot = (dayIndex: number, slotId: string) => {
    const updatedSchedule = [...schedule];
    updatedSchedule[dayIndex].timeSlots = updatedSchedule[dayIndex].timeSlots.filter(
      slot => slot.id !== slotId
    );
    setSchedule(updatedSchedule);
  };

  // Update time slot values
  const updateTimeSlot = (dayIndex: number, slotId: string, field: 'startTime' | 'endTime', value: string) => {
    const updatedSchedule = [...schedule];
    const slotIndex = updatedSchedule[dayIndex].timeSlots.findIndex(slot => slot.id === slotId);

    if (slotIndex !== -1) {
      updatedSchedule[dayIndex].timeSlots[slotIndex][field] = value;
      setSchedule(updatedSchedule);
    }
  };

  // Convert schedule data to API payload format
  const prepareAvailabilityPayload = (): AvailabilityPayload => {
    const timeSlot: AvailabilityPayload['timeSlot'] = {
      monday: [],
      tuesday: [],
      wednesday: [],
      thursday: [],
      friday: [],
      saturday: [],
      sunday: []
    };

    // Map day abbreviations to full day names for the API
    const dayMapping: Record<string, keyof AvailabilityPayload['timeSlot']> = {
      'SUN': 'sunday',
      'MON': 'monday',
      'TUE': 'tuesday',
      'WED': 'wednesday',
      'THU': 'thursday',
      'FRI': 'friday',
      'SAT': 'saturday'
    };

    // Convert each day's schedule to the API format
    schedule.forEach(day => {
      const apiDayKey = dayMapping[day.day];

      if (day.isAvailable && day.timeSlots.length > 0) {
        timeSlot[apiDayKey] = day.timeSlots.map(slot => ({
          start: slot.startTime,
          end: slot.endTime
        }));
      } else {
        timeSlot[apiDayKey] = [];
      }
    });

    return {
      providerId,
      timeSlot
    };
  };

  // Handle form submission
  const handleSubmit = () => {
    if (!providerId) {
      showToast("Provider ID not found. Please go back and complete the previous steps first.", "error");
      return;
    }

    // Check if at least one day has availability
    const hasAvailability = schedule.some(day => day.isAvailable && day.timeSlots.length > 0);
    if (!hasAvailability) {
      showToast("Please set availability for at least one day", "error");
      return;
    }

    // Prepare and send the data
    const payload = prepareAvailabilityPayload();
    console.log("Sending availability data:", payload);
    setAvailability(payload);
  };

  return (
    <div className="w-full">
      {/* Days of the week with time slots */}
      <div className="space-y-4 mb-8">
        {schedule.map((day, dayIndex) => (
          <div key={day.day} className="flex items-center gap-6">
            {/* Day checkbox */}
            <div className="flex items-center w-16">
              <Checkbox
                id={`day-${day.day}`}
                checked={day.isAvailable}
                onCheckedChange={() => toggleDayAvailability(dayIndex)}
                className="data-[state=checked]:bg-orange-1 data-[state=checked]:text-white data-[state=checked]:border-0 mr-2"
              />
              <Label htmlFor={`day-${day.day}`} className="font-medium text-gray-700">
                {day.day}
              </Label>
            </div>

            {/* Time slots or "Unavailable" text */}
            <div className="flex-1">
              {day.isAvailable ? (
                day.timeSlots.map((slot, slotIndex) => (
                  <div key={slot.id} className="flex items-center mb-2">
                    <div className="w-24">
                      <TimePicker
                        value={slot.startTime}
                        onChange={(value) => updateTimeSlot(dayIndex, slot.id, 'startTime', value)}
                      />
                    </div>
                    <span className="mx-2">-</span>
                    <div className="w-24">
                      <TimePicker
                        value={slot.endTime}
                        onChange={(value) => updateTimeSlot(dayIndex, slot.id, 'endTime', value)}
                      />
                    </div>

                    {slotIndex === 0 ? (
                      <Button
                        onClick={() => addTimeSlot(dayIndex)}
                        variant="ghost"
                        size="icon"
                        className="ml-2 text-orange-1"
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    ) : (
                      <Button
                        onClick={() => removeTimeSlot(dayIndex, slot.id)}
                        variant="ghost"
                        size="icon"
                        className="ml-2 text-gray-500"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                ))
              ) : (
                <div
                  className="flex items-center h-10 cursor-pointer hover:bg-gray-50 rounded-md px-2 transition-colors"
                  onClick={() => toggleDayAvailability(dayIndex)}
                >
                  <span className="text-gray-500">Unavailable</span>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Navigation Buttons */}
      <div className="flex mt-6">
        <Button
          onClick={onBack}
          variant="ghost"
          className="w-full rounded-full"
          disabled={isPending}
        >
          Back
        </Button>
        <Button
          onClick={handleSubmit}
          className="bg-orange-1 w-full rounded-full px-8"
          disabled={isPending || !providerId}
        >
          {isPending ? "Saving..." : "Next"}
        </Button>
      </div>
    </div>
  );
}
