import { useState, useCallback, useMemo } from "react";
import Layout from "../components/Layout";
import SellerTable from "../components/SellerTable";
import { useMutation, useQuery } from "@tanstack/react-query";
import { ApproveSellersApi, fetchSellersApi } from "../api";
import { showToast } from "@/lib/toast";
import SellerDetailsModal from "../components/SellerDetailsModal";
import SellerProductsModal from "../components/SellerProductsModal";
import ProductDetailsModal from "../components/ProductDetailsModal";

function Sellers() {
  const [isOpen, setIsOpen] = useState({
    sellerDetails: false,
    sellerProducts: false,
    productDetails: false,
  });
  const onOpen = useCallback((modal: keyof typeof isOpen) => {
    setIsOpen((prev) => ({ ...prev, [modal]: true }));
  }, []);

  const onClose = useCallback((modal: keyof typeof isOpen) => {
    setIsOpen((prev) => ({ ...prev, [modal]: false }));
  }, []);

  const onOpenSellerModal = useCallback(
    (id: string) => {
      onOpen("sellerDetails");
      setSelectedSellerId(id);
    },
    [onOpen]
  );
  const [currentPage, setCurrentPage] = useState(1);
  const [focus, setFocus] = useState("all");
  const [selectedSellerId, setSelectedSellerId] = useState("");
  const [selectedProductId, setSelectedProductId] = useState("");

  const {
    data,
    isPending: loading,
    isSuccess,
    refetch,
  } = useQuery({
    queryKey: ["sellers", currentPage, focus],
    queryFn: () => fetchSellersApi({ type: focus, page: currentPage }),
  });

  const totalPages = useMemo(() => {
    return isSuccess ? data?.data?.totalPages || 1 : 1;
  }, [isSuccess, data?.data?.totalPages]);

  const { mutate } = useMutation({
    mutationFn: ApproveSellersApi,
    onSuccess: ({ data }: { data: { seller: { isApproved: boolean } } }) => {
      refetch();
      showToast(
        `Seller ${
          data.seller.isApproved ? "approved" : "rejected"
        } successfully!`,
        "success"
      );
    },
    onError: () => {
      showToast("failed to approve seller", "error");
    },
  });

  const handleSellerApprove = useCallback(
    (sellerId: string, isApproved: boolean) => {
      mutate({ sellerId, isApproved });
    },
    [mutate]
  );

  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  // Memoized modal callback functions
  const modalCallbacks = useMemo(
    () => ({
      onCloseSellerDetails: () => onClose("sellerDetails"),
      onOpenProducts: () => onOpen("sellerProducts"),
      onCloseSellerProducts: () => onClose("sellerProducts"),
      onOpenProductDetails: () => onOpen("productDetails"),
      onOpenSellerDetails: () => onOpen("sellerDetails"),
      onCloseProductDetails: () => onClose("productDetails"),
      onOpenSellerProducts: () => onOpen("sellerProducts"),
    }),
    [onOpen, onClose]
  );

  const handleProduct = useCallback((productId: string) => {
    setSelectedProductId(productId);
  }, []);

  return (
    <>
      <Layout>
        <div className="p-6 bg-white rounded-lg">
          <div className="mb-6">
            <h1 className="mb-2 text-2xl font-bold">Sellers</h1>
            <div className="flex space-x-4">
              <button
                onClick={useCallback(() => setFocus("all"), [])}
                className={`${
                  focus === "all" ? "border-b-4 border-orange-1 font-bold" : ""
                }  font-medium pb-2`}
              >
                All
              </button>
              <button
                onClick={useCallback(() => setFocus("pending"), [])}
                className={`${
                  focus === "pending"
                    ? "border-b-4 border-orange-1 font-bold"
                    : ""
                }  font-medium pb-2`}
              >
                Pending verification
              </button>
            </div>
          </div>

          <SellerTable
            data={data?.data?.sellers}
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            focus={focus}
            loading={loading}
            handleSellerApprove={handleSellerApprove}
            onOpen={onOpenSellerModal}
          />
        </div>
      </Layout>
      {isOpen.sellerDetails && (
        <SellerDetailsModal
          isOpen={isOpen.sellerDetails}
          onClose={modalCallbacks.onCloseSellerDetails}
          onOpenProducts={modalCallbacks.onOpenProducts}
          selectedSellerId={selectedSellerId}
        />
      )}
      {isOpen.sellerProducts && (
        <SellerProductsModal
          isOpen={isOpen.sellerProducts}
          onClose={modalCallbacks.onCloseSellerProducts}
          selectedSellerId={selectedSellerId}
          onOpenProductDetails={modalCallbacks.onOpenProductDetails}
          openSellerDetails={modalCallbacks.onOpenSellerDetails}
          handleProduct={handleProduct}
        />
      )}
      {isOpen.productDetails && (
        <ProductDetailsModal
          isOpen={isOpen.productDetails}
          onClose={modalCallbacks.onCloseProductDetails}
          openSellerProducts={modalCallbacks.onOpenSellerProducts}
          selectedProductId={selectedProductId}
        />
      )}
    </>
  );
}

export default Sellers;
