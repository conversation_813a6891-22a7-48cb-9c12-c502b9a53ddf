function RecentChatHeader({
  name,
  lastMessage,
  unreadCount,
  image = undefined,
}: {
  name: string;
  lastMessage: string;
  unreadCount: number;
  image?: string | undefined;
}) {
  return (
    <div className="flex justify-between p-4 cursor-pointer ">
      <div className="flex grow gap-x-2">
        {image && (
          <div className="flex-shrink-0 w-12 h-12 overflow-hidden rounded-full">
            <img
              src={image}
              alt={'profile'}
              className="object-cover w-full h-full"
            />
          </div>
        )}
        <div>
          <h1>{name}</h1>
          <h5 className="max-w-[200px] md:max-w-[300px] text-sm truncate text-neutral-300">
            {lastMessage}
          </h5>
        </div>
      </div>
      <div className="min-w-5 h-5 max-w-[50px] my-auto rounded-full bg-orange-1">
        <h5 className="text-sm text-center text-white truncate  max-w-[50px]">{unreadCount}</h5>
      </div>
    </div>
  );
}

export default RecentChatHeader;
