import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { X } from "lucide-react";
import CustomSlider from "@/components/ui/CustomSlider";
import { fetchBrandsApi, fetchCategoriesApi } from "../api";
import { useQuery } from "@tanstack/react-query";
import { FilterProps } from "../type";

function FilterSection({
  selectedCategories,
  selectedBrands,
  priceRange,
  removeCategory,
  addCategory,
  addBrand,
  removeBrand,
  setPriceRange,
}: FilterProps) {
  const categoriesData = useQuery({
    queryKey: ["category"],
    queryFn: fetchCategoriesApi,
  });
  const brandsData = useQuery({
    queryKey: ["brands"],
    queryFn: fetchBrandsApi,
  });

  const categories = categoriesData.isSuccess
    ? categoriesData.data.data.categories
    : [];
  const brands = brandsData.isSuccess ? brandsData.data.data.brands : [];

  return (
    <div className="p-6 space-y-6 border rounded-lg border-neutral-40">
      <h2 className="font-medium">Filter by</h2>

      {/* Category Section */}
      <div className="space-y-3">
        <h3 className="font-medium">Category</h3>

        {/* Selected Categories */}
        <div className="flex flex-wrap gap-2 mb-3">
          {selectedCategories.map((category: string) => (
            <div
              key={category}
              className="flex items-center gap-1 px-3 py-1.5 rounded-full bg-tints-70"
            >
              <span className="text-sm">{category}</span>
              <button
                onClick={() => removeCategory(category)}
                className="text-neutral-600 hover:text-neutral-900"
              >
                <X size={14} />
              </button>
            </div>
          ))}
        </div>

        {/* Category Dropdown */}
        <Select onValueChange={addCategory}>
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select" />
          </SelectTrigger>
          <SelectContent>
            {categories?.map((category: string) => (
              <SelectItem key={category} value={category}>
                {category}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Price Range Section */}
      <div className="space-y-3">
        <div className="flex justify-between">
          <h3 className="font-medium">Price range</h3>
          <span className="text-neutral-300">
            ${priceRange[0]}-${priceRange[1]}+
          </span>
        </div>

        <div className="pt-4 pb-2">
          {/* Custom slider with orange thumbs */}
          <CustomSlider
            defaultValue={[priceRange[0], priceRange[1]]}
            max={5000}
            step={100}
            value={priceRange}
            onValueChange={setPriceRange}
            className="mt-2"
          />
        </div>
      </div>

      {/* Brand Section */}
      <div className="space-y-3">
        <h3 className="font-medium">Brand</h3>

        {/* Selected Brands */}
        <div className="flex flex-wrap gap-2 mb-3">
          {selectedBrands?.map((brand) => (
            <div
              key={brand}
              className="flex items-center gap-1 px-3 py-1.5 rounded-full bg-tints-70"
            >
              <span className="text-sm">{brand}</span>
              <button
                onClick={() => removeBrand(brand)}
                className="text-neutral-600 hover:text-neutral-900"
              >
                <X size={14} />
              </button>
            </div>
          ))}
        </div>

        {/* Brand Dropdown */}
        <Select onValueChange={addBrand}>
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select" />
          </SelectTrigger>
          <SelectContent>
            {brands?.map((brand: string) => (
              <SelectItem key={brand} value={brand}>
                {brand}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}

export default FilterSection;
