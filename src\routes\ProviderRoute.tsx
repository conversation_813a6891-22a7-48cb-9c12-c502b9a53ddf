import { Route, Routes } from "react-router-dom";
import ProtectedRoutes from "./ProtectedRoutes";
import ProviderRegister from "@/features/auth-provider/pages/Register";
import ProviderLogin from "@/features/auth-provider/pages/Login";
import ProviderOtp from "@/features/auth-provider/pages/Otp";
import ProviderProfileSetup from "@/features/auth-provider/pages/ProfileSetup";
import PlanSelection from "@/features/auth-provider/pages/PlanSelection";
import SetupComplete from "@/features/auth-provider/pages/SetupComplete";
import ForgotPassword from "@/features/auth-provider/pages/ForgotPassword";
import CheckEmail from "@/features/auth-provider/pages/CheckEmail";
import ProviderResetPassword from "@/features/auth-provider/pages/ResetPassword";
import SubscriptionFailure from "@/features/auth-provider/pages/SubscriptionFailure";
import NotFound from "@/components/ui/not-found";
import Dashboard from "@/features/provider-dashboard/pages/Dashboard";
import ServicesPage from "@/features/provider-dashboard/pages/Services";
import ServiceDetails from "@/features/provider-dashboard/pages/ServiceDetails";
import ProfilePreview from "@/features/provider-dashboard/pages/ProfilePreview";
import Profile from "@/features/provider-dashboard/pages/Profile";

function ProviderRoute() {
  return (
    <Routes>
      {/* Temporarily exposing routes for testing */}
      <Route path={"/dashboard-preview"} element={<Dashboard />} />
      <Route path={"/services-preview"} element={<ServicesPage />} />
      <Route path={"/services-preview/:serviceId"} element={<ServiceDetails />} />
      <Route path={"/profile-preview/*"} element={<ProfilePreview />} />

      <Route element={<ProtectedRoutes role="provider" />}>
        <Route path={"/dashboard"} element={<Dashboard />} />
        <Route path={"/services"} element={<ServicesPage />} />
        <Route path={"/services/:serviceId"} element={<ServiceDetails />} />
        <Route path={"/messages"} element={<div>Messages Page</div>} />
        <Route path={"/reviews"} element={<div>Reviews Page</div>} />
        <Route path={"/profile/*"} element={<Profile />} />
      </Route>
      <Route path={"/register"} element={<ProviderRegister />} />
      <Route path={"/login"} element={<ProviderLogin />} />
      <Route path={"/verify-otp"} element={<ProviderOtp />} />
      <Route path={"/setup-profile"} element={<ProviderProfileSetup />} />
      <Route path={"/setup-profile/plan-selection"} element={<PlanSelection />} />
      <Route path={"/setup-profile/pending-verification"} element={<SetupComplete />} />
      <Route path={"/setup-profile/subscription-failure"} element={<SubscriptionFailure />} />
      <Route path={"/forgot-password"} element={<ForgotPassword />} />
      <Route path={"/forgot-password/check-email"} element={<CheckEmail />} />
      <Route path={"/reset-password"} element={<ProviderResetPassword />} />
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
}

export default ProviderRoute;
