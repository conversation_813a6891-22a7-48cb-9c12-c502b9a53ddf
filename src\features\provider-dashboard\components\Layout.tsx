
import Footer from "./Footer";
import Header from "./Header";
import Navbar from "./NavBar";

function Layout({
  children,
  showFooter,
}: {
  children: React.ReactNode;
  showFooter?: boolean;
}) {
  return (
    <div>
      <div className="h-screen w-[100%] gap-x-5 hidden md:flex">
        <div className="h-[100vh] min-w-[220px] max-w-[220px] bg-tints-50">
          <Navbar />
        </div>
        <div className="grow p-6 overflow-y-auto">{children}</div>
      </div>
      <div className="h-screen w-[100%] flex flex-col md:hidden">
        <div className="w-full mt-4 mb-5 border-b-2 border-gray-2">
          <Header />
        </div>
        <div className={`px-4 flex-1 overflow-y-auto ${showFooter ? 'pb-20' : ''}`}>
          {children}
        </div>
        {showFooter && <Footer />}
      </div>
    </div>
  );
}

export default Layout;
