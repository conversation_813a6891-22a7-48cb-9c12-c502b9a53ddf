import { fetchRefreshTokenApi, logoutApi } from "@/api";
import { showToast } from "@/lib/toast";
import { create } from "zustand";

type User = {
  accessToken: string;
  role: string;
};
type AuthStore = {
  user: User | null;
  login: (user: User) => void;
  logout: (role?: string) => Promise<void>;
  setAccesstoken: () => Promise<string>;
  authReset: () => void;
};

export const useAuthStore = create<AuthStore>((set) => ({
  user: null,
  login: (user: User) => {
    set({ user });
    localStorage.setItem("nurtureUser", user.role);
  },
  authReset: () => {
    showToast("Session expired. Please login again.", "error");
    localStorage.removeItem("nurtureUser");
    localStorage.removeItem("hasCompletedQuiz");
    localStorage.removeItem("showCarePlan");
    set({ user: null });
  },
  logout: async () => {
    try {
      await logoutApi();
      const role = localStorage.getItem("nurtureUser") as string;
      if (role === "customer") {
        window.location.href = "/";
      } else if (role === "seller") {
        window.location.href = "/seller/login";
      } else if (role === "provider") {
        window.location.href = "/provider/login";
      } else if (role === "admin") {
        window.location.href = "/admin/login";
      }
      localStorage.removeItem("nurtureUser");
      localStorage.removeItem("hasCompletedQuiz");
      localStorage.removeItem("showCarePlan");
      set(() => ({ user: null }));
    } catch (error) {
      console.log(error, "error");
    }
  },
  setAccesstoken: async () => {
    try {
      const role = localStorage.getItem("nurtureUser") as string;
      const { data } = await fetchRefreshTokenApi(role);
      set({ user: { accessToken: data.accessToken, role } });
      return data.accessToken;
    } catch {
      return "";
    }
  },
}));
