export type MenuDrawerProps = {
  open: boolean;
  onClose: () => void;
};

export type ServiceProviderProps = {
  id: number;
  image: string;
  name: string;
  specialty: string;
  rating: number;
  experience: string;
  services: string[];
};

export type ProductQueryParams = {
  page?: string;
  category?: string;
  minPrice?: string;
  brand?: string;
};

export type Product = {
  _id: string;
  images: { image: string; key: string }[];
  title: string;
  seller: { companyName: string };
  price: string;
  totalRating: number;
};

export type CarePlanCardItem = {
  title: string;
  description: string;
  frequency: string;
  category: string;
};