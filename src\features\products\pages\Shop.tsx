import PageLayout from "@/components/layout/PageLayout";
import ProductCard from "@/components/ui/productCard";
import { useQuery } from "@tanstack/react-query";
import { fetchProductsApi } from "../api";
import { Product } from "../type";
import FilterSection from "../components/FilterSection";
import DynamicPagination from "@/components/ui/dynamic-pagination";
import { useState } from "react";
import MobileFilterSection from "../components/MobileFilterSection";
import ProductSkelton from "@/features/products/components/ProductSkelton";
import useDebounce from "@/hooks/useDebounce";

function Shop() {
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedBrands, setSelectedBrands] = useState<string[]>([]);
  const [priceRange, setPriceRange] = useState<number[]>([0, 5000]);
  const [currentPage, setCurrentPage] = useState(1);
  const debouncedPriceRange = useDebounce(priceRange, 1000);
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  const removeCategory = (category: string) => {
    setSelectedCategories(selectedCategories.filter((cat) => cat !== category));
  };
  const addCategory = (value: string) => {
    setSelectedCategories([...selectedCategories, value]);
  };

  const addBrand = (value: string) => {
    setSelectedBrands([...selectedBrands, value]);
  };
  const removeBrand = (brand: string) => {
    setSelectedBrands(selectedBrands.filter((b) => b !== brand));
  };
  const clearFilter = () => {
    setSelectedBrands([]);
    setSelectedCategories([]);
    setPriceRange([0, 5000]);
  };
  const {
    data,
    isSuccess,
    isPending: loading,
  } = useQuery({
    queryKey: [
      "products",
      selectedBrands,
      selectedCategories,
      debouncedPriceRange,
      currentPage,
    ],
    queryFn: () =>
      fetchProductsApi({
        category: selectedCategories.join(","),
        brand: selectedBrands.join(","),
        minPrice: priceRange[0].toString(),
        maxPrice: priceRange[1].toString(),
        page: currentPage,
      }),
  });
  const totalPages = isSuccess ? data.data?.totalPages : 1;
  return (
    <PageLayout>
      <div>
        <div>
          <div className="flex flex-col justify-between w-11/12 mx-auto mt-5 md:mt-10 gap-y-5 md:gap-y-0 md:flex-row gap-x-4">
            <div className="hidden w-full md:block md:w-1/4">
              <FilterSection
                addBrand={addBrand}
                addCategory={addCategory}
                removeCategory={removeCategory}
                removeBrand={removeBrand}
                selectedBrands={selectedBrands}
                selectedCategories={selectedCategories}
                priceRange={priceRange}
                setPriceRange={setPriceRange}
              />
            </div>
            <div className="block w-full md:hidden">
              <MobileFilterSection
                addBrand={addBrand}
                addCategory={addCategory}
                removeCategory={removeCategory}
                removeBrand={removeBrand}
                selectedBrands={selectedBrands}
                selectedCategories={selectedCategories}
                priceRange={priceRange}
                setPriceRange={setPriceRange}
                clearFilter={clearFilter}
              />
            </div>
            <div className="grid max-h-[90vh] overflow-y-scroll scrollbar-hide grid-cols-12 md:w-3/4 gap-x-2 gap-y-5">
              {loading ? (
                // Loading skeleton
                [1, 2, 3, 4, 5, 6].map((_, i) => (
                  <ProductSkelton
                    className="col-span-6 mx-3 md:mx-0 md:col-span-4 "
                    key={i}
                  />
                ))
              ) : isSuccess && data?.data?.products?.length > 0 ? (
                // Products found
                data.data.products.map((product: Product) => (
                  <div
                    key={product._id}
                    className="col-span-6 mx-3 md:mx-0 md:col-span-4 min-w-[180px] border p-4 rounded-lg border-neutral-40 max-w-[350px] md:max-w-[300px]"
                  >
                    <ProductCard
                      id={product._id}
                      image={product.images[0]}
                      title={product.title}
                      brand={product.seller.companyName}
                      price={product.price}
                      rating={product.totalRating}
                    />
                  </div>
                ))
              ) : (
                // No products found
                <div className="w-full h-full col-span-12 md:my-16">
                  <div className="flex items-center justify-center w-full h-full">
                    <h1 className="text-xl font-semibold text-center text-orange-1">
                      No Products Found
                    </h1>
                  </div>
                </div>
              )}
            </div>
          </div>
          {isSuccess && data?.data?.products?.length > 0 && (
            <div className="flex justify-center mt-5">
              <DynamicPagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={handlePageChange}
              />
            </div>
          )}
        </div>
      </div>
    </PageLayout>
  );
}

export default Shop;
