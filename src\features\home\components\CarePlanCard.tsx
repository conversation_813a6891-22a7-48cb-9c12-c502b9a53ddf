import { CarePlanCardItem } from "../types";
import calendarIcon from "@/assets/calendar.svg";

const getCategoryColor = (category: string): string => {
  switch (category.toLowerCase()) {
    case "physical":
      return "bg-blue-100 text-blue-800";
    case "cultural":
      return "bg-purple-100 text-purple-800";
    case "emotional":
      return "bg-pink-100 text-pink-800";
    case "nutritional":
      return "bg-green-100 text-green-800";
    case "rest":
      return "bg-amber-100 text-amber-800";
    case "mindfulness":
      return "bg-indigo-100 text-indigo-800";
    case "mental":
      return "bg-emerald-100 text-emerald-800";
    case "social":
      return "bg-yellow-100 text-yellow-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

// Helper function to get frequency icon/indicator
const getFrequencyIndicator = (frequency: string): string => {
  switch (frequency.toLowerCase()) {
    case "daily":
      return "text-red-600";
    case "weekly":
      return "text-blue-600";
    case "monthly":
      return "text-green-600";
    case "as needed":
      return "text-purple-600";
    default:
      return "text-gray-600";
  }
};
function CarePlanCard({ item }: { item: CarePlanCardItem }) {
  const categoryStyle = getCategoryColor(item.category);
  const text = getFrequencyIndicator(item.frequency);

  return (
    <div className="py-4 border-b border-gray-200 last:border-b-0">
      <div className="flex flex-col gap-2 mb-2 md:flex-row md:items-center md:justify-between">
        <h3 className="text-lg font-semibold text-gray-800">{item.title}</h3>
        <div className="flex items-center gap-2">
          <span
            className={`px-3 py-1 rounded-full text-xs font-medium ${categoryStyle}`}
          >
            {item.category}
          </span>
        </div>
      </div>
      <p className="text-gray-600">{item.description}</p>
      <div className="flex items-center mt-2 gap-x-1">
        <img src={calendarIcon} alt="calendar" className="w-4 h-4" />
        <span className={`text-sm font-medium ${text}`}>{item.frequency}</span>
      </div>
    </div>
  );
}

export default CarePlanCard;
