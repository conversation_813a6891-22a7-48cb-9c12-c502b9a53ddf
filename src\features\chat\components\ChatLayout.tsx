import { useCallback, useState } from "react";
import ChatSearch from "./ChatSearch";
import ChatList from "./ChatList";
import MessageSection from "./MessageSection";

function ChatLayout() {
  const [search, setSearch] = useState<string>("");
  const [hideMessageSection, setHideMessageSection] = useState(true);
  const handleHideMessageSection = (status: boolean) => {
    setHideMessageSection(status);
  };
  const handleSearch = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value);
  }, []);
  const [selectedChat, setSelectedChat] = useState<string | null>(null);
  const handleChatSelect = useCallback((chatId: string) => {
    setSelectedChat(chatId);
  }, []);
  return (
    <div className="grid w-full  h-[90vh] grid-cols-12 mx-auto rounded-sm">
      <div className="col-span-12 py-4 md:border-r md:col-span-4 border-neutral-40">
        <div className="flex flex-col md:mr-5">
          <h1 className="text-lg">Messaging</h1>
          <ChatSearch search={search} handleSearch={handleSearch} />
        </div>
        {/* chat list */}
        <div className="mt-4">
          <ChatList
            selectedChatId={selectedChat}
            onChatSelect={handleChatSelect}
            handleHideMessageSection={() => handleHideMessageSection(false)}
          />
        </div>
      </div>
      <div
        className={`${hideMessageSection ? "hidden md:block" : "block"} max-h-[90vh] col-span-12 border md:block md:col-span-8 md:rounded-e-sm border-neutral-40`}
      >
        <MessageSection
          handleHideMessageSection={() => handleHideMessageSection(true)}
          name="Marvin McKinney"
          description="seller"
        />
      </div>
    </div>
  );
}

export default ChatLayout;
