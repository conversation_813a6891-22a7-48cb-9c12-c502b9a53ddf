import { useCallback, useState } from "react";
import ChatSearch from "./ChatSearch";
import ChatList from "./ChatList";
import MessageSection from "./MessageSection";

function ChatLayout() {
  const [search, setSearch] = useState<string>("");
  const [hideMessageSection, setHideMessageSection] = useState(true);
  const handleHideMessageSection = (status: boolean) => {
    setHideMessageSection(status);
  };
  const handleSearch = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value);
  }, []);
  const [selectedChat, setSelectedChat] = useState<string | null>(null);
  const handleChatSelect = useCallback((chatId: string) => {
    setSelectedChat(chatId);
  }, []);
  return (
    <div className="grid w-full grid-cols-12 mx-auto rounded-sm">
      <div className={`${hideMessageSection ? "block " : "hidden md:block"} col-span-12 md:py-4 md:border-x md:col-span-4 border-neutral-40`}>
        <div className="flex flex-col px-3">
          <h1 className="mb-1 text-lg">Messaging</h1>
          <ChatSearch search={search} handleSearch={handleSearch} />
        </div>
        {/* chat list */}
        <div className="mt-4">
          <ChatList
            selectedChatId={selectedChat}
            onChatSelect={handleChatSelect}
            handleHideMessageSection={() => handleHideMessageSection(false)}
          />
        </div>
      </div>
      <div
        className={`${hideMessageSection ? "hidden md:block" : "block"} max-h-[100vh] col-span-12 md:border md:block md:col-span-8 md:rounded-e-sm border-neutral-40`}
      >
        <MessageSection
          handleHideMessageSection={() => handleHideMessageSection(true)}
          name="Marvin McKinney"
          description="seller"
        />
      </div>
    </div>
  );
}

export default ChatLayout;
