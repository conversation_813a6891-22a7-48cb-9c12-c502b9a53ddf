import { useState } from "react";
import { useForm } from "react-hook-form";
import { joiResolver } from "@hookform/resolvers/joi";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { X } from "lucide-react";
import { ImageCropper } from "@/components/ui/ImageCropper";
import { editProfileSchema } from "../../../auth-provider/validation";

interface EditProfileDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: EditProfileFormData) => void;
}

export interface EditProfileFormData {
  firstName: string;
  lastName: string;
  phone: string;
  specialty: string;
  businessName: string;
  taxId: string;
  yearsOfExperience: string;
  serviceLocation: string[];
  photo?: File;
}

// Define allowed file types (same as Step 1)
const providerAllowedFileTypes = {
  profilePicture: ["image/jpeg", "image/png", "image/jpg", "image/webp"],
};

type FileWithPreview = File & {
  preview: string;
};

export default function EditProfileDialog({
  open,
  onOpenChange,
  onSubmit,
}: EditProfileDialogProps) {
  // State for image cropper (same as Step 1)
  const [selectedFile, setSelectedFile] = useState<FileWithPreview | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);

  // State for service location radio selection
  const [selectedServiceLocation, setSelectedServiceLocation] = useState<string>("");

  const {
    register,
    handleSubmit,
    setValue,
    trigger,
    formState: { errors },
  } = useForm<EditProfileFormData>({
    resolver: joiResolver(editProfileSchema),
  });

  // Handle file selection for profile photo (same as Step 1)
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0] as FileWithPreview;
      file.preview = URL.createObjectURL(file);
      setSelectedFile(file);
      setDialogOpen(true);
    }
  };

  // Handle cropped image (same as Step 1)
  const handleCroppedImage = (file: File) => {
    setValue("photo", file);
    trigger("photo");
  };

  // Handle form submission with single service location
  const handleFormSubmit = (data: EditProfileFormData) => {
    // Validate service location
    if (!selectedServiceLocation) {
      // You can add toast here if needed
      return;
    }

    // Set the service location value as single item array
    setValue("serviceLocation", [selectedServiceLocation]);

    const formData = {
      ...data,
      serviceLocation: [selectedServiceLocation],
    };
    onSubmit(formData);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md mx-auto max-h-[90vh] overflow-y-auto">
        <DialogHeader className="flex flex-row items-center justify-between">
          <DialogTitle className="text-lg font-semibold">Edit profile</DialogTitle>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => onOpenChange(false)}
            className="h-6 w-6"
          >
            <X className="h-4 w-4" />
          </Button>
        </DialogHeader>

        <div className="w-full">
          {/* Photo Upload Section (same as Step 1) */}
          <div className="flex flex-col items-center mb-6">
            {selectedFile ? (
              <ImageCropper
                dialogOpen={dialogOpen}
                setDialogOpen={setDialogOpen}
                selectedFile={selectedFile}
                setSelectedFile={setSelectedFile}
                onImageCropped={handleCroppedImage}
              />
            ) : (
              <div
                className="w-36 h-36 rounded-full bg-red-50 flex items-center justify-center cursor-pointer border-2 border-dashed border-red-200"
                onClick={() => document.getElementById('photo-upload')?.click()}
              >
                <div className="text-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mx-auto text-red-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  <span className="text-sm text-gray-500 mt-1">Upload photo</span>
                </div>
                <input
                  id="photo-upload"
                  type="file"
                  accept={providerAllowedFileTypes.profilePicture.join(',')}
                  className="hidden"
                  onChange={handleFileSelect}
                />
              </div>
            )}
            {errors.photo && (
              <p className="text-sm text-left text-red-500 mt-2">{errors.photo.message}</p>
            )}
          </div>

          <form className="space-y-4" onSubmit={handleSubmit(handleFormSubmit)}>
            {/* Name Fields (same as Step 1) */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Input
                  placeholder="First name"
                  {...register("firstName")}
                  className="border-input-border focus:border-slate-300"
                />
                {errors.firstName && (
                  <p className="text-sm text-left text-red-500">{errors.firstName.message}</p>
                )}
              </div>
              <div>
                <Input
                  placeholder="Last name"
                  {...register("lastName")}
                  className="border-input-border focus:border-slate-300"
                />
                {errors.lastName && (
                  <p className="text-sm text-left text-red-500">{errors.lastName.message}</p>
                )}
              </div>
            </div>

            {/* Phone Number (same as Step 1) */}
            <div>
              <Input
                placeholder="Phone number"
                {...register("phone")}
                className="border-input-border focus:border-slate-300"
              />
              {errors.phone && (
                <p className="text-sm text-left text-red-500">{errors.phone.message}</p>
              )}
            </div>

            {/* Specialty (same as Step 1) */}
            <div>
              <Input
                placeholder="Specialty"
                {...register("specialty")}
                className="border-input-border focus:border-slate-300"
              />
              {errors.specialty && (
                <p className="text-sm text-left text-red-500">{errors.specialty.message}</p>
              )}
            </div>

            {/* Business Name (same as Step 1) */}
            <div>
              <Input
                placeholder="Business name"
                {...register("businessName")}
                className="border-input-border focus:border-slate-300"
              />
              {errors.businessName && (
                <p className="text-sm text-left text-red-500">{errors.businessName.message}</p>
              )}
            </div>

            {/* Tax ID (same as Step 1) */}
            <div>
              <Input
                placeholder="Tax ID"
                {...register("taxId")}
                className="border-input-border focus:border-slate-300"
              />
              {errors.taxId && (
                <p className="text-sm text-left text-red-500">{errors.taxId.message}</p>
              )}
            </div>

            {/* Years of Experience (same as Step 1) */}
            <div>
              <Input
                type="number"
                placeholder="Years of experience"
                min="0"
                max="50"
                {...register("yearsOfExperience")}
                className="border-input-border focus:border-slate-300"
                onKeyDown={(e) => {
                  // Only allow numbers, backspace, delete, tab, and arrow keys
                  if (!/[0-9]/.test(e.key) && !['Backspace', 'Delete', 'Tab', 'ArrowLeft', 'ArrowRight'].includes(e.key)) {
                    e.preventDefault();
                  }
                }}
              />
              {errors.yearsOfExperience && (
                <p className="text-sm text-left text-red-500">{errors.yearsOfExperience.message}</p>
              )}
            </div>

            {/* Preferred Service Location (radio buttons) */}
            <div>
              <p className="text-base text-left text-[#665F5D] mb-4">Preferred service location</p>
              <div className="grid grid-cols-2 space-x-6 mb-2">
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="virtual"
                    name="serviceLocation"
                    value="virtual"
                    checked={selectedServiceLocation === "virtual"}
                    onChange={(e) => setSelectedServiceLocation(e.target.value)}
                    className="w-4 h-4 text-orange-500 border-gray-300 focus:ring-orange-500"
                  />
                  <Label htmlFor="virtual">Virtual</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="in-person"
                    name="serviceLocation"
                    value="in-person"
                    checked={selectedServiceLocation === "in-person"}
                    onChange={(e) => setSelectedServiceLocation(e.target.value)}
                    className="w-4 h-4 text-orange-500 border-gray-300 focus:ring-orange-500"
                  />
                  <Label htmlFor="in-person">In person</Label>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="flex-1"
              >
                Save
              </Button>
            </div>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
