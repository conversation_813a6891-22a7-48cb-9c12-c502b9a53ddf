import Footer from "./SellerFooter";
import Header from "./SellerHeader";
import Navbar from "./SellerNavBar";

function SellerLayout({
  children,
  showFooter,
}: {
  children: React.ReactNode;
  showFooter?: boolean;
}) {
  return (
    <div>
      <div className="h-screen w-[100%]   hidden md:flex">
        <div className="h-[100vh] min-w-[220px] max-w-[220px] bg-tints-50">
          <Navbar />
        </div>
        <div className=" grow">
          <div className="mx-5 max-h-[95%] overflow-y-scroll scrollbar-hide">
            {children}
          </div>
        </div>
      </div>
      <div className="h-screen w-[100%] flex flex-col justify-between  md:hidden">
        <div className="w-full my-4 border-b-2 border-gray-2 ">
          <Header />
        </div>
        <div className="px-4 grow">{children}</div>
        {showFooter && <Footer />}
      </div>
    </div>
  );
}

export default SellerLayout;
