import React from "react";
import RatingIcon from "@/assets/rating-star.png";
import { Link } from "react-router-dom";

interface ProductCardProps {
  image: { image: string; key: string };
  title: string;
  brand: string;
  price: string;
  rating: number;
  id: string;
}

const ProductCard: React.FC<ProductCardProps> = ({
  image,
  title,
  brand,
  price,
  rating,
  id,
}) => {
  return (
    <div className="flex flex-col w-full h-full cursor-grab active:cursor-grabbing">
      <Link
        to={`/product/${id}`}
        className="flex-grow mb-2 overflow-hidden bg-white rounded-lg"
      >
        <img
          src={image.image}
          alt={title}
          className="object-cover w-full h-auto transition duration-300 ease-in-out delay-75 aspect-square hover:scale-105"
        />
      </Link>
      <div className="mt-2">
        <Link
          to={`/product/${id}`}
          className="text-sm font-medium md:text-base line-clamp-2 hover:underline"
        >
          {title}
        </Link>
        <p className="mt-1 text-xs text-gray-500">{brand}</p>
        <div className="flex items-center justify-between mt-2">
          <span className="font-semibold">${price}</span>
          <div className="flex items-center">
            <img src={RatingIcon} className="w-5 h-5" />
            <span className="text-sm text-gray-600">
              {rating > 0 ? rating : ""}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductCard;
