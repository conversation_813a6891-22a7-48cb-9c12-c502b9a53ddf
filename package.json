{"name": "nurture-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fontsource/public-sans": "^5.2.5", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.3", "@smastrom/react-rating": "^1.5.0", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.0", "@tanstack/react-query": "^5.72.1", "add": "^2.0.6", "aws-amplify": "^6.14.4", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "graphql": "15.10.1", "joi": "^17.13.3", "lodash": "^4.17.21", "lucide-react": "^0.487.0", "quill": "^2.0.3", "react": "^18.2.0", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-hook-form": "^7.55.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-image-crop": "^11.0.10", "react-infinite-scroll-component": "^6.1.0", "react-quill": "^2.0.0", "react-router-dom": "^7.5.0", "react-spinners": "^0.16.1", "shadcn": "^2.5.0", "table": "^6.9.0", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "zustand": "^5.0.3"}, "devDependencies": {"@aws-amplify/backend": "^1.16.1", "@aws-amplify/backend-cli": "^1.7.1", "@eslint/js": "^9.21.0", "@types/node": "^22.14.0", "@types/react": "^18.3.21", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "aws-cdk": "^2.1003.0", "aws-cdk-lib": "^2.189.1", "constructs": "^10.4.2", "esbuild": "^0.25.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "tsx": "^4.19.4", "typescript": "^5.8.3", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}, "overrides": {"graphql": "15.10.1"}}