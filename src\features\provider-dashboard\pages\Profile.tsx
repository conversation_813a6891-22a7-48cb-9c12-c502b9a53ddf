import { useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import Layout from "../components/Layout";
import ProfileSidebar from "../components/profile/ProfileSidebar";
import BasicDetails from "../components/profile/BasicDetails";
import Availability from "../components/profile/Availability";
import Subscription from "../components/profile/Subscription";

function Profile() {
  const location = useLocation();
  const navigate = useNavigate();

  // Redirect to basic details by default
  useEffect(() => {
    if (location.pathname === "/provider/profile") {
      navigate("/provider/profile/basic-details");
    } else if (location.pathname === "/provider/profile-preview") {
      navigate("/provider/profile-preview/basic-details");
    }
  }, [location.pathname, navigate]);

  // If we're at the root profile path, don't render anything (we'll redirect)
  if (location.pathname === "/provider/profile" || location.pathname === "/provider/profile-preview") {
    return null;
  }

  return (
    <Layout>
      <div className="w-full">
        <div className="flex flex-col md:flex-row gap-8 md:gap-0">
          {/* Profile Sidebar */}
          <div className="w-full md:w-[240px] border-r border-gray-200 mr-6 shrink-0">
            <ProfileSidebar />
          </div>

          {/* Main Content */}
          <div className="flex-1">
            {(location.pathname === "/provider/profile/basic-details" ||
              location.pathname === "/provider/profile-preview/basic-details") && <BasicDetails />}
            {(location.pathname === "/provider/profile/availability" ||
              location.pathname === "/provider/profile-preview/availability") && <Availability />}
            {(location.pathname === "/provider/profile/promotional-offers" ||
              location.pathname === "/provider/profile-preview/promotional-offers") && <div>Promotional Offers Content</div>}
            {(location.pathname === "/provider/profile/subscription" ||
              location.pathname === "/provider/profile-preview/subscription") && <Subscription />}
          </div>
        </div>
      </div>
    </Layout>
  );
}

export default Profile;
