import api from "@/lib/axios";

type Product = {
  id: string;
  title: string;
};
type Service = {
  id: string;
  title: string;
};
type SearchResult = {
  products: Product[];
  services: Service[];
  totalProducts: number;
  totalServices: number;
};
type addRecentSearchApiPayload = {
  type: string;
  item: {
    id: string;
    title: string;
  };
};

export const fetchRefreshTokenApi = async (role: string) => {
  const { data } = await api.post("/api/v1/auth/refresh-token", { role });
  return data;
};

export const logoutApi = async () => {
  const { data } = await api.delete("/api/v1/auth/logout");
  return data;
};

export const fetchRecentSearchApi = async (): Promise<SearchResult> => {
  const { data } = await api.get("/api/v1/search/recent-search");
  return data?.data?.recentSearch;
};

export const addRecentSearchApi = async (
  payload: addRecentSearchApiPayload
) => {
  console.log("Adding recent search with API:", payload);
  const { data } = await api.post("/api/v1/search/recent-search", payload);
  return data;
};

export const fetchProductAndServiceApi = async (
  search: string
): Promise<SearchResult> => {
  const { data } = await api.get("/api/v1/search", { params: { search } });
  return data?.data;
};
