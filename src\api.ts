import api from "@/lib/axios";

export const fetchRefreshTokenApi = async (role: string) => {
  const { data } = await api.post("/api/v1/auth/refresh-token", { role });
  return data;
};

export const logoutApi = async () => {
  const { data } = await api.delete("/api/v1/auth/logout");
  return data;
};

export const fetchRecentSearchApi = async () => {
  // const { data } = await api.get("/api/v1/product/recent-search");
  // return data;
  return {
    data: {
      products: [
        {
          _id: "680b7fe49a796d4b812376a7",
          title: "The Outfit 3 Piece Set",
        },
        {
          _id: "680b827c57fe7a25a46db669",
          title: "The Outfit 3 Piece Set",
        },
      ],
      services: [
        {
          _id: "1",
          title: "Infant Care Nursing",
        },
        {
          _id: "2",
          title: "Infant Care Nursing",
        },
      ],
    },
  };
};

export const fetchProductAndServiceApi = async (search: string) => {
  await new Promise((resolve) => setTimeout(resolve, 2000));
  // const { data } = await api.get("/api/v1/product/service", { params: { search } });
  // return data;
  console.log(search);
  return {
    data: {
      products: [
        {
          _id: "680b7fe49a796d4b812376a7",
          title: "abcd",
        },
        {
          _id: "680b827c57fe7a25a46db669",
          title: "The Outfit 3 Piece Set",
        },
      ],
      services: [],
    },
  };
};
