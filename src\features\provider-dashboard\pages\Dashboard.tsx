import Layout from "@/components/layout/ProviderLayout";
import StatsCard from "../components/StatsCard";
import BookingSection from "../components/BookingSection";
import { LuCalendarClock, LuCalendarCheck, LuCalendarPlus } from "react-icons/lu";

function Dashboard() {
  // Mock data - would come from API in real implementation
  const userName = "User";
  const totalBookings = 12;
  const totalRevenue = 8000;

  const newBookings = [
    {
      id: "1",
      name: "<PERSON>",
      date: "1 Jan 2025",
      time: "2:00pm-2:15pm",
    },
    {
      id: "2",
      name: "<PERSON>",
      date: "1 Jan 2025",
      time: "2:15pm-3:00pm",
    },
  ];

  const upcomingSessions = [
    {
      id: "3",
      name: "<PERSON>",
      date: "1 Jan 2025",
      time: "2:00pm-3:00pm",
    },
  ];

  const completedSessions = [
    {
      id: "4",
      name: "<PERSON>",
      date: "1 Jan 2025",
      time: "2:00pm-3:00pm",
    },
    {
      id: "5",
      name: "<PERSON>",
      date: "1 Jan 2025",
      time: "2:00pm-3:00pm",
    },
  ];

  return (
    <Layout showFooter={true}>
      <div className="mx-auto ">
        {/* Header section */}
        <div className="mb-8">
          <h1 className="text-[20px] font-semibold">Hi {userName},</h1>
          <p className="text-gray-600">here's an overview of your activity</p>
        </div>

        {/* Stats cards */}
        <div className="grid grid-cols-1 gap-6 mb-10 md:grid-cols-2">
          <StatsCard title="Total bookings" value={totalBookings} />
          <StatsCard title="Total revenue" value={totalRevenue} prefix="$" />
        </div>

        {/* Discovery calls section */}
        <div className="mb-10">
          <h2 className="mb-1 text-base font-semibold">Discovery Calls</h2>
          <p className="mb-6 text-gray-400">These are the 15 minute discovery calls requested by people.</p>

          <BookingSection
            title="New bookings"
            icon={<LuCalendarPlus className="w-5 h-5" />}
            count={newBookings.length}
            bookings={newBookings}
            type="pending"
          />

          <BookingSection
            title="Upcoming sessions"
            icon={<LuCalendarClock className="w-5 h-5" />}
            count={upcomingSessions.length}
            bookings={upcomingSessions}
            type="upcoming"
          />

          <BookingSection
            title="Completed sessions"
            icon={<LuCalendarCheck className="w-5 h-5" />}
            count={completedSessions.length}
            bookings={completedSessions}
            type="completed"
          />
        </div>
      </div>
    </Layout>
  );
}

export default Dashboard;
