import { useState } from "react";
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogTitle, DialogClose } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { X, Plus, Trash2 } from "lucide-react";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { zodResolver } from "@hookform/resolvers/zod";

export type ServiceHighlight = {
  text: string;
  id: string;
};

export type ServiceDetails = {
  serviceName: string;
  duration: string;
  highlights: ServiceHighlight[];
  price: string;
  description: string;
};

type ServiceDetailsDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: ServiceDetails) => void;
};

// Zod schema for form validation
const serviceDetailsSchema = z.object({
  serviceName: z.string().min(1, "Service name is required"),
  duration: z.string().min(1, "Duration is required"),
  price: z.string().min(1, "Price is required"),
  description: z.string().min(10, "Description is required"),
});

type FormData = z.infer<typeof serviceDetailsSchema>;

export default function ServiceDetailsDialog({
  open,
  onOpenChange,
  onSubmit,
}: ServiceDetailsDialogProps) {
  const [highlights, setHighlights] = useState<ServiceHighlight[]>([
    { text: "", id: crypto.randomUUID() },
  ]);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<FormData>({
    resolver: zodResolver(serviceDetailsSchema),
    defaultValues: {
      serviceName: "",
      duration: "",
      price: "",
      description: "",
    },
  });

  const handleAddHighlight = () => {
    setHighlights([...highlights, { text: "", id: crypto.randomUUID() }]);
  };

  const handleRemoveHighlight = (id: string) => {
    if (highlights.length > 1) {
      setHighlights(highlights.filter((highlight) => highlight.id !== id));
    }
  };

  const handleHighlightChange = (id: string, value: string) => {
    setHighlights(
      highlights.map((highlight) =>
        highlight.id === id ? { ...highlight, text: value } : highlight
      )
    );
  };

  const handleFormSubmit = (data: FormData) => {
    // Filter out empty highlights
    const validHighlights = highlights.filter((h) => h.text.trim() !== "");

    const serviceData: ServiceDetails = {
      ...data,
      highlights: validHighlights,
    };

    onSubmit(serviceData);

    // Reset form
    reset();
    setHighlights([{ text: "", id: crypto.randomUUID() }]);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px] p-0 gap-0 overflow-hidden">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <DialogTitle className="text-lg font-semibold">
              Enter service details
            </DialogTitle>
            <DialogClose className="rounded-full h-6 w-6 flex items-center justify-center">
              <X className="h-4 w-4" />
            </DialogClose>
          </div>

          <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
            {/* Service Name */}
            <div>
              <Input
                placeholder="Service name"
                {...register("serviceName")}
                className="border-input-border focus:border-slate-300"
              />
              {errors.serviceName && (
                <p className="text-sm text-red-500 mt-1">
                  {errors.serviceName.message}
                </p>
              )}
            </div>

            {/* Duration */}
            <div>
              <Input
                placeholder="Duration"
                {...register("duration")}
                className="border-input-border focus:border-slate-300"
              />
              {errors.duration && (
                <p className="text-sm text-red-500 mt-1">
                  {errors.duration.message}
                </p>
              )}
            </div>

            {/* Service Highlights */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-gray-600">Service highlights</span>
                <Button
                  type="button"
                  onClick={handleAddHighlight}
                  variant="ghost"
                  size="sm"
                  className="h-8 px-2 text-orange-1"
                >
                  Add <Plus className="h-4 w-4 ml-1" />
                </Button>
              </div>

              <div className="space-y-2">
                {highlights.map((highlight) => (
                  <div key={highlight.id} className="flex gap-2">
                    <Input
                      placeholder="Type here"
                      value={highlight.text}
                      onChange={(e) =>
                        handleHighlightChange(highlight.id, e.target.value)
                      }
                      className="border-input-border focus:border-slate-300"
                    />
                    <Button
                      type="button"
                      onClick={() => handleRemoveHighlight(highlight.id)}
                      variant="ghost"
                      size="icon"
                      className="h-10 w-10 text-gray-500"
                      disabled={highlights.length === 1}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>

            {/* Price */}
            <div className="relative">
              <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
                $
              </span>
              <Input
                placeholder="Pricing"
                {...register("price")}
                className="pl-7 border-input-border focus:border-slate-300"
              />
              {errors.price && (
                <p className="text-sm text-red-500 mt-1">
                  {errors.price.message}
                </p>
              )}
            </div>

            {/* Service Description */}
            <div>
              <Textarea
                placeholder="Service description"
                {...register("description")}
                className="min-h-[120px] border-input-border focus:border-slate-300"
              />
              {errors.description && (
                <p className="text-sm text-red-500 mt-1">
                  {errors.description.message}
                </p>
              )}
            </div>

            {/* Submit Button */}
            <Button type="submit" className="w-full bg-orange-1 hover:bg-orange-1/90">
              Submit
            </Button>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
