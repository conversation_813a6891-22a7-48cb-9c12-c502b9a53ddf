import { Modal } from "@/components/ui/modal";
import fingerPrintLogo from "@/assets/fingerprint.png";
import CloseIcon from "@/assets/close-icon.png";

import { fetchCarePlanApi } from "../api";
import { useQuery } from "@tanstack/react-query";
import CarePlanCardSkelton from "../components/CarePlanCardSkelton";
import { CarePlanCardItem } from "../types";
import CarePlanCard from "./CarePlanCard";
function CarePlanModal({
  isOpen,
  onClose,
}: {
  isOpen: boolean;
  onClose: () => void;
}) {
  const {
    data,
    isPending: loading,
    isSuccess: success,
  } = useQuery({
    queryKey: ["care-plan"],
    queryFn: fetchCarePlanApi,
  });
  if (success) {
    localStorage.setItem("hasCompletedQuiz", "true");
  }
  return (
    <Modal className="md:min-w-[700px]" isOpen={isOpen} onClose={onClose}>
      <div className="flex justify-end">
        <img
          onClick={onClose}
          className="cursor-pointer"
          src={CloseIcon}
          alt="close"
        />
      </div>
      <div className="bg-white rounded-lg   p-6 max-h-[80vh] overflow-y-auto scrollbar-hide ">
        {/* Header Section */}
        <div className="flex flex-col justify-between mb-6 md:flex-row">
          <div className="flex flex-col md:flex-row md:items-center">
            <img
              src={fingerPrintLogo}
              alt="nurture"
              className="w-12 h-12 mb-4 mr-4 md:mb-0"
            />
            <div>
              <h1 className="text-2xl font-bold text-gray-800">
                Your Personalized Care Plan
              </h1>
              <p className="text-gray-600">
                Tailored recommendations for your postpartum journey
              </p>
            </div>
          </div>
        </div>
        <div className="p-5 bg-white rounded-lg">
          {loading ? (
            [1, 2].map((_, i) => <CarePlanCardSkelton key={i} />)
          ) : !data?.length ? (
            <div className="py-10 text-center">
              <div className="mb-4 text-5xl text-gray-400">📋</div>
              <h3 className="text-lg font-medium text-gray-700">
                No care plan items yet
              </h3>
              <p className="text-gray-500">
                Your personalized care plan will appear here once it's ready.
              </p>
            </div>
          ) : (
            data?.map((plan: CarePlanCardItem) => (
              <div className="space-y-4 border-t ">
                <CarePlanCard key={plan.description} item={plan} />
              </div>
            ))
          )}
        </div>
      </div>
    </Modal>
  );
}

export default CarePlanModal;
