import { Button } from "@/components/ui/button";
import { Modal } from "@/components/ui/modal";
import StarRating from "@/components/ui/StarRating";
import { Textarea } from "@/components/ui/textarea";
import { useMutation } from "@tanstack/react-query";

import { useState } from "react";
import { submitReviewApi } from "../api";
import { showToast } from "@/lib/toast";
function ReviewModal({
  productId,
  isOpen,
  onClose,
}: {
  productId: string;
  isOpen: boolean;
  onClose: () => void;
}) {
  const [rating, setRating] = useState(0);
  const [review, setReview] = useState("");
  const onReviewChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setReview(e.target.value);
  };
  const { mutate, isPending: loading } = useMutation({
    mutationFn: submitReviewApi,
    onSuccess: () => {
      showToast("review submitted successfully", "success");
      onClose();
    },
    onError: () => {
      showToast("Failed to submit review", "error");
    },
  });
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!rating) return;
    mutate({ review, rating, productId });
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <form onSubmit={handleSubmit} className="flex flex-col gap-y-6">
        <h1 className="text-lg font-semibold">
          Give a review for this product
        </h1>
        <div className="flex justify-center">
          <StarRating rating={rating} setRating={setRating} />
        </div>
        <Textarea
          onChange={(e) => onReviewChange(e)}
          value={review}
          placeholder="Type here..."
          className="border border-neutral-50 "
        />
        <Button disabled={loading} className="w-full">
          {loading ? "submitting" : "submit"}
        </Button>
      </form>
    </Modal>
  );
}

export default ReviewModal;
