import { useState, useEffect } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import Layout from "../components/Layout";
import BookingSection from "../components/BookingSection";
import { Button } from "@/components/ui/button";
import {  Check, ChevronRight, Pencil, Trash2 } from "lucide-react";
import { LuCalendarClock, LuCalendarCheck, LuCalendarPlus } from "react-icons/lu";
import { getServiceDetailsApi, type ServiceDetails } from "../api";

// Mock booking data
const mockBookings = {
  newBookings: [
    { id: "1", name: "<PERSON>", date: "1 Jan 2025", time: "2:00pm - 3:00pm" },
    { id: "2", name: "<PERSON>", date: "1 Jan 2025", time: "2:00pm - 3:00pm" }
  ],
  upcomingSessions: [
    { id: "3", name: "<PERSON>", date: "1 Jan 2025", time: "2:00pm - 3:00pm" }
  ],
  completedSessions: [
    { id: "4", name: "<PERSON>", date: "1 Jan 2025", time: "2:00pm - 3:00pm" },
    { id: "5", name: "<PERSON>en", date: "1 <PERSON> 2025", time: "2:00pm - 3:00pm" }
  ]
};

function ServiceDetails() {
  const { serviceId } = useParams();
  const navigate = useNavigate();
  const [service, setService] = useState<ServiceDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch service details
  useEffect(() => {
    const fetchServiceDetails = async () => {
      if (!serviceId) {
        setError("Service ID is required");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        const response = await getServiceDetailsApi(serviceId);
        setService(response.data.service);
      } catch (err) {
        console.error("Error fetching service details:", err);
        setError("Failed to load service details. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    fetchServiceDetails();
  }, [serviceId]);

  const handleBackToServices = () => {
    navigate("/provider/services");
  };

  const handleEdit = () => {
    // TODO: Implement edit functionality
    console.log("Edit service:", serviceId);
  };

  const handleDelete = () => {
    // TODO: Implement delete functionality
    console.log("Delete service:", serviceId);
  };

  if (loading) {
    return (
      <Layout showFooter={true}>
        <div className="flex justify-center items-center h-64">
          <div className="text-lg">Loading service details...</div>
        </div>
      </Layout>
    );
  }

  if (error || !service) {
    return (
      <Layout showFooter={true}>
        <div className="flex flex-col justify-center items-center h-64 gap-4">
          <div className="text-red-500 text-lg">{error || "Service not found"}</div>
          <Button onClick={handleBackToServices} variant="outline">
            Back to Services
          </Button>
        </div>
      </Layout>
    );
  }

  return (
    <Layout showFooter={true}>
      <div className="w-full">
        {/* Header with breadcrumb and actions */}
        <div className="flex flex-col md:flex-row md:items-center justify-between mb-6 gap-4">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBackToServices}
              className="p-0 h-auto text-base hover:bg-transparent text-gray-600 hover:text-gray-800"
            >
              Services
            </Button>
            <span className="text-gray-400"> <ChevronRight className="w-4 h-4" />
            </span>
            <span className="text-gray-900  text-lg font-medium">{service.title}</span>
          </div>

          <div className="flex items-center">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleEdit}
              className="flex items-center gap-1 border-gray-300 hover:bg-gray-50"
            >
              <Pencil className="w-5 h-5" />

            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDelete}
              className="flex items-center gap-1 text-red-600 border-red-200 hover:bg-red-50"
            >
              <Trash2 className="w-5 h-5" />

            </Button>
          </div>
        </div>

        {/* Service Details Card */}
        <div className="border border-gray-200 rounded-lg p-6 mb-6">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h1 className="text-xl font-semibold mb-1">{service.title}</h1>
              <p className="text-gray-600">{service.duration > 1 ? `${service.duration} hours` : `${service.duration} hour`}</p>
            </div>
            <div>
              <span className="text-xl font-semibold">${service.price}</span>
            </div>
          </div>

          <div className="mb-6">
            <p className="text-gray-700 leading-relaxed">{service.description}</p>
          </div>

          {service.highlights && service.highlights.length > 0 && (
            <div className="border border-tints-50 bg-tints-30 rounded-lg p-6 mb-8">
              <h2 className="text-xs text-gray-700 mb-4">SERVICE HIGHLIGHTS</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                {service.highlights.map((highlight: string, index: number) => (
                  <div key={index} className="flex items-start gap-2">
                    <Check className=" w-4 h-4  flex-shrink-0"/>
                    <span className="text-gray-700 text-sm">{highlight}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

        </div>

        {/* Service Highlights */}

        {/* Bookings Section */}
        <div className="mb-10">
          <BookingSection
            title="New bookings"
            icon={<LuCalendarPlus className="w-5 h-5" />}
            count={mockBookings.newBookings.length}
            bookings={mockBookings.newBookings}
            type="pending"
          />

          <BookingSection
            title="Upcoming sessions"
            icon={<LuCalendarClock className="w-5 h-5" />}
            count={mockBookings.upcomingSessions.length}
            bookings={mockBookings.upcomingSessions}
            type="upcoming"
          />

          <BookingSection
            title="Completed sessions"
            icon={<LuCalendarCheck className="w-5 h-5" />}
            count={mockBookings.completedSessions.length}
            bookings={mockBookings.completedSessions}
            type="completed"
          />
        </div>
      </div>
    </Layout>
  );
}

export default ServiceDetails;
