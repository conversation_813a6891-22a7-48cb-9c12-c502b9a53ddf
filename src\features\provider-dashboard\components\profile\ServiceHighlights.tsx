import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, Trash2 } from "lucide-react";
import { useState } from "react";

interface ServiceHighlight {
  id: string;
  text: string;
}

interface ServiceHighlightsProps {
  highlights: ServiceHighlight[];
}

function ServiceHighlights({ highlights = [] }: ServiceHighlightsProps) {
  const [isAdding, setIsAdding] = useState(false);
  const [newHighlight, setNewHighlight] = useState("");

  const handleAdd = () => {
    // Here you would typically save the new highlight to your backend
    setIsAdding(false);
    setNewHighlight("");
  };

  return (
    <div className="mb-8">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-base ">Service highlights</h2>
        <Button 
          variant="outline"
          onClick={() => setIsAdding(true)}
          className="flex items-center gap-2 text-orange-1 border-orange-1"
        >
          Add <Plus className="w-4 h-4" />
        </Button>
      </div>
      
      {isAdding && (
        <div className="mb-4 space-y-4">
          <Input 
            value={newHighlight}
            onChange={(e) => setNewHighlight(e.target.value)}
            placeholder="Enter service highlight"
          />
          <div className="flex gap-2 justify-end">
            <Button 
              variant="outline"
              onClick={() => {
                setIsAdding(false);
                setNewHighlight("");
              }}
            >
              Cancel
            </Button>
            <Button onClick={handleAdd}>
              Add
            </Button>
          </div>
        </div>
      )}
      
      {highlights.length > 0 ? (
        <div className="space-y-3">
          {highlights.map((highlight) => (
            <div 
              key={highlight.id}
              className="flex justify-between items-center p-4 border border-gray-200 rounded-lg"
            >
              <span className="text-base text-neutral-300">{highlight.text}</span>
              <Button 
                variant="ghost" 
                size="sm"
                className="text-gray-500 hover:text-red-500"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>
          ))}
        </div>
      ) : (
        <p className="text-gray-500">No service highlights added yet.</p>
      )}
    </div>
  );
}

export default ServiceHighlights;
