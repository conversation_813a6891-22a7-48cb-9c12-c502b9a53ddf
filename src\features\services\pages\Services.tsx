import { useState, useMemo } from "react";
import PageLayout from "@/components/layout/PageLayout";
import ServiceFilter from "../components/ServiceFilter";
import MobileServiceFilter from "../components/MobileServiceFilter";
import ServiceProviderCard from "../components/ServiceProviderCard";
import {
  ServiceProvider,
  ApiServiceProvider,
  ServiceQueryParams,
} from "../types";
import { fetchServicesApi } from "../api";
import ServiceBanner from "@/assets/service-banner.png";
import useDebounce from "@/hooks/useDebounce";
import { useGlobalSearchStore } from "@/store/searchStore";
import { useQuery } from "@tanstack/react-query";

function Services() {
  // Global search integration
  const { getGlobalSearch } = useGlobalSearchStore((state) => state);
  const globalSearch = getGlobalSearch();
  const debouncedSearch = useDebounce(globalSearch, 800);
  const memoizedSearch = useMemo(() => debouncedSearch, [debouncedSearch]);

  // Filter state
  const [selectedSpecialties, setSelectedSpecialties] = useState<string[]>([]);
  const [selectedLocations, setSelectedLocations] = useState<string[]>([]);
  const [priceRange, setPriceRange] = useState<number[]>([0, 500]);
  const [selectedAvailability, setSelectedAvailability] = useState<string[]>(
    []
  );

  // API state
  const [currentPage] = useState(1);

  // Transform API data to UI format
  const transformApiDataToUI = (
    apiData: ApiServiceProvider[]
  ): ServiceProvider[] => {
    return apiData.map((item) => ({
      _id: item.provider.id,
      image:
        item.provider.profilePicture[0]?.image ||
        item.provider.profilePicture[0]?.url ||
        ServiceBanner,
      name: item.provider.firstName,
      specialty: item.provider.specialty,
      rating: 4.5, // This will need to be added to API response later
      experience: `${item.provider.experience} yrs exp`,
      location: item.provider.serviceLocation.join(", "),
      description: item.services.map((s) => s.title).join(", "),
      services: item.services.map((s) => s.title),
    }));
  };

  // API query parameters
  const queryParams: ServiceQueryParams = useMemo(() => ({
    page: currentPage,
    ...(memoizedSearch && { search: memoizedSearch }),
    ...(selectedSpecialties.length > 0 && { speciality: selectedSpecialties.join(",") }),
    ...(selectedLocations.length > 0 && { location: selectedLocations.join(",") }),
    ...(priceRange[0] > 0 && { minPrice: priceRange[0] }),
    ...(priceRange[1] > 0 && { maxPrice: priceRange[1] }),
  }), [currentPage, memoizedSearch, selectedSpecialties, selectedLocations, priceRange]);

  // Fetch services using useQuery
  const {
    data,
    isSuccess,
    isPending: loading,
    error,
  } = useQuery({
    queryKey: [
      "services",
      currentPage,
      selectedSpecialties,
      selectedLocations,
      priceRange,
      memoizedSearch,
    ],
    queryFn: () => fetchServicesApi(queryParams),
  });

  // Transform API data to UI format
  const serviceProviders: ServiceProvider[] = useMemo(() => {
    if (isSuccess && data?.data?.services) {
      return transformApiDataToUI(data.data.services);
    }
    return [];
  }, [isSuccess, data]);

  // Filter handlers
  const addSpecialty = (specialty: string) => {
    if (!selectedSpecialties.includes(specialty)) {
      setSelectedSpecialties([...selectedSpecialties, specialty]);
    }
  };

  const removeSpecialty = (specialty: string) => {
    setSelectedSpecialties(selectedSpecialties.filter((s) => s !== specialty));
  };

  const addLocation = (location: string) => {
    if (!selectedLocations.includes(location)) {
      setSelectedLocations([...selectedLocations, location]);
    }
  };

  const removeLocation = (location: string) => {
    setSelectedLocations(selectedLocations.filter((l) => l !== location));
  };

  const addAvailability = (availability: string) => {
    if (!selectedAvailability.includes(availability)) {
      setSelectedAvailability([...selectedAvailability, availability]);
    }
  };

  const removeAvailability = (availability: string) => {
    setSelectedAvailability(
      selectedAvailability.filter((a) => a !== availability)
    );
  };

  const clearFilter = () => {
    setSelectedSpecialties([]);
    setSelectedLocations([]);
    setPriceRange([0, 500]);
    setSelectedAvailability([]);
  };

  return (
    <PageLayout isShopOrServicePage={true}>
      <div>
        <div className="flex flex-col justify-between w-11/12 mx-auto mt-5 md:mt-10 gap-y-5 md:gap-y-0 md:flex-row gap-x-4">
          {/* Desktop Filter Section */}
          <div className="hidden w-full md:block md:w-1/4">
            <ServiceFilter
              selectedSpecialties={selectedSpecialties}
              selectedLocations={selectedLocations}
              priceRange={priceRange}
              selectedAvailability={selectedAvailability}
              removeSpecialty={removeSpecialty}
              addSpecialty={addSpecialty}
              addLocation={addLocation}
              removeLocation={removeLocation}
              setPriceRange={setPriceRange}
              addAvailability={addAvailability}
              removeAvailability={removeAvailability}
            />
          </div>

          {/* Mobile Filter Section */}
          <div className="block w-full md:hidden">
            <MobileServiceFilter
              selectedSpecialties={selectedSpecialties}
              selectedLocations={selectedLocations}
              priceRange={priceRange}
              selectedAvailability={selectedAvailability}
              removeSpecialty={removeSpecialty}
              addSpecialty={addSpecialty}
              addLocation={addLocation}
              removeLocation={removeLocation}
              setPriceRange={setPriceRange}
              addAvailability={addAvailability}
              removeAvailability={removeAvailability}
              clearFilter={clearFilter}
            />
          </div>

          {/* Service Providers Grid */}
          <div className="w-full md:w-3/4">
            {loading ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-lg">Loading services...</div>
              </div>
            ) : error ? (
              <div className="flex justify-center items-center h-64">
                <div className="text-red-500 text-lg">Failed to fetch services. Please try again.</div>
              </div>
            ) : serviceProviders.length === 0 ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-lg text-gray-500">No services found</div>
              </div>
            ) : (
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                {serviceProviders.map((provider) => (
                  <ServiceProviderCard key={provider._id} provider={provider} />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </PageLayout>
  );
}

export default Services;
