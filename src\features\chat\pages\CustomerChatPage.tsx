import { useCallback, useState, useEffect, useMemo } from "react";
import { useLocation } from "react-router-dom";

import ChatSearch from "../components/ChatSearch";
import CustomerChatList from "../components/CustomerChatList";
import PageLayout from "@/components/layout/PageLayout";
import CustomerMessageSection from "../components/CustomerMessageSection";
import { SelectedChat } from "../type";

import CustomerSupportIcon from "@/assets/customer-support.svg";
import NextIcon from "@/assets/next.svg";

function CustomerChatPage() {
  const location = useLocation();
  const chatDetails = location.state;
  const memoizedChatDetails = useMemo(
    (): SelectedChat | null => chatDetails,
    [chatDetails]
  );
  const [search, setSearch] = useState<string>("");
  const [hideMessageSection, setHideMessageSection] = useState(true);
  const handleHideMessageSection = (status: boolean) => {
    setHideMessageSection(status);
  };
  const handleSearch = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value);
  }, []);
  const [selectedChat, setSelectedChat] = useState<SelectedChat>(
    {} as SelectedChat
  );
  const handleChatSelect = useCallback((data: SelectedChat) => {
    setSelectedChat(data);
  }, []);

  useEffect(() => {
    if (memoizedChatDetails) {
      setSelectedChat({
        name: memoizedChatDetails.name,
        description: memoizedChatDetails.description,
        receiverId: memoizedChatDetails.receiverId,
        profilePicture: memoizedChatDetails.profilePicture,
        role: memoizedChatDetails.role,
      });
      setHideMessageSection(false);
    }
    return () => {
      setSelectedChat({} as SelectedChat);
    };
  }, [memoizedChatDetails]);

  return (
    <PageLayout showFooter={false}>
      <div className="grid w-11/12 md:min-h-[80vh]  md:max-h-[80vh] grid-cols-12 mx-auto rounded-sm">
        {/* Left side */}
        <div
          className={`${hideMessageSection ? "block " : "hidden md:block"} col-span-12 py-4 border  md:col-span-4 md:rounded-s-sm border-neutral-40`}
        >
          <div className="flex flex-col px-4 gap-y-2">
            <h1 className="text-lg">Messaging</h1>
            <ChatSearch search={search} handleSearch={handleSearch} />
            {/* Customer Support */}
            <div className="flex justify-between mt-4">
              <div className="flex gap-x-2">
                <div className="p-2 my-auto rounded-full bg-tints-50">
                  <img src={CustomerSupportIcon} alt="customer support" />
                </div>
                <div>
                  <h1 className="md:text-lg">Customer Support</h1>
                  <h5 className="text-sm text-neutral-300">
                    Reach out to nurture if you need any help
                  </h5>
                </div>
              </div>
              <img
                src={NextIcon}
                alt="next"
                className="w-4 h-4 my-auto cursor-pointer"
                onClick={() => handleHideMessageSection(false)}
              />
            </div>
          </div>
          {/* chat list */}
          <div className="mt-7">
            <CustomerChatList
              selectedChatId={
                selectedChat?.conversationId || selectedChat?.receiverId
              }
              onChatSelect={handleChatSelect}
              handleHideMessageSection={() => handleHideMessageSection(false)}
              currentFocus={memoizedChatDetails?.role as "seller" | "provider"}
            />
          </div>
        </div>
        {/* chat section */}
        <div
          className={`${hideMessageSection ? "hidden md:block" : "block"} col-span-12 border md:block md:col-span-8 md:rounded-e-sm border-neutral-40`}
        >
          <CustomerMessageSection
            handleHideMessageSection={() => handleHideMessageSection(true)}
            name={selectedChat?.name || ""}
            description={selectedChat?.description || ""}
            image={selectedChat?.profilePicture?.url}
            receiverId={selectedChat?.receiverId}
            conversationId={selectedChat?.conversationId}
            role={selectedChat?.role}
          />
        </div>
      </div>
    </PageLayout>
  );
}

export default CustomerChatPage;
