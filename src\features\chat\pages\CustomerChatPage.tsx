import ChatSearch from "../components/ChatSearch";
import CustomerSupportIcon from "@/assets/customer-support.svg";
import NextIcon from "@/assets/next.svg";

import { useCallback, useState, useEffect } from "react";
import CustomerChatList from "../components/CustomerChatList";
import PageLayout from "@/components/layout/PageLayout";
import CustomerMessageSection from "../components/CustomerMessageSection";
import { Chat, SelectedChat } from "../type";
import { useLocation } from "react-router-dom";

// Type for navigation state
type NavigationState = {
  receiverId: string;
  name: string;
  description: string;
};

function CustomerChatPage() {
  const location = useLocation();
  const chatDetails = location.state as NavigationState | null;

  const [search, setSearch] = useState<string>("");
  const [hideMessageSection, setHideMessageSection] = useState(true);
  const handleHideMessageSection = (status: boolean) => {
    setHideMessageSection(status);
  };
  const handleSearch = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value);
  }, []);
  const [selectedChat, setSelectedChat] = useState<SelectedChat | null>(null);
  const handleChatSelect = useCallback((data: SelectedChat) => {
    setSelectedChat(data);
  }, []);

  // Debug logging - let's see what we're getting
  console.log("=== NAVIGATION DEBUG ===");
  console.log("Full location object:", location);
  console.log("location.state:", location.state);
  console.log("chatDetails (typed state):", chatDetails);
  console.log("typeof location.state:", typeof location.state);
  console.log("========================");

  // Handle navigation state data
  useEffect(() => {
    console.log("useEffect triggered with chatDetails:", chatDetails);

    if (chatDetails) {
      console.log("chatDetails exists, checking properties...");
      console.log("receiverId:", chatDetails.receiverId);
      console.log("name:", chatDetails.name);
      console.log("description:", chatDetails.description);

      if (chatDetails.receiverId && chatDetails.name) {
        console.log("✅ Navigation state is valid, creating chat...");

        // Create a SelectedChat object from navigation state
        const chatFromNavigation: SelectedChat = {
          conversationId: chatDetails.receiverId,
          name: chatDetails.name,
          description: chatDetails.description || "",
          profilePicture: { url: "", key: "" },
          receiverId: chatDetails.receiverId,
        };

        // Set the selected chat and show message section
        setSelectedChat(chatFromNavigation);
        setHideMessageSection(false);

        console.log("✅ Chat initialized from navigation:", chatFromNavigation);
      } else {
        console.log("❌ Missing required properties in chatDetails");
      }
    } else {
      console.log("❌ No chatDetails found in navigation state");
    }
  }, [chatDetails]);

  console.log("Current selectedChat:", selectedChat);
  return (
    <PageLayout showFooter={false}>
      <div className="grid w-11/12 md:min-h-[80vh]  md:max-h-[80vh] grid-cols-12 mx-auto rounded-sm">
        {/* Left side */}
        <div
          className={`${hideMessageSection ? "block " : "hidden md:block"} col-span-12 py-4 border  md:col-span-4 md:rounded-s-sm border-neutral-40`}
        >
          <div className="flex flex-col px-4 gap-y-2">
            <h1 className="text-lg">Messaging</h1>
            <ChatSearch search={search} handleSearch={handleSearch} />
            {/* Customer Support */}
            <div className="flex justify-between mt-4">
              <div className="flex gap-x-2">
                <div className="p-2 my-auto rounded-full bg-tints-50">
                  <img src={CustomerSupportIcon} alt="customer support" />
                </div>
                <div>
                  <h1 className="md:text-lg">Customer Support</h1>
                  <h5 className="text-sm text-neutral-300">
                    Reach out to nurture if you need any help
                  </h5>
                </div>
              </div>
              <img
                src={NextIcon}
                alt="next"
                className="w-4 h-4 my-auto cursor-pointer"
                onClick={() => handleHideMessageSection(false)}
              />
            </div>
          </div>
          {/* chat list */}
          <div className="mt-7">
            <CustomerChatList
              selectedChatId={selectedChat?.conversationId || null}
              onChatSelect={handleChatSelect}
              handleHideMessageSection={() => handleHideMessageSection(false)}
            />
          </div>
        </div>
        {/* chat section */}
        <div
          className={`${hideMessageSection ? "hidden md:block" : "block"} col-span-12 border md:block md:col-span-8 md:rounded-e-sm border-neutral-40`}
        >
          <CustomerMessageSection
            handleHideMessageSection={() => handleHideMessageSection(true)}
            name={selectedChat?.name || ""}
            description={selectedChat?.description || ""}
            image={selectedChat?.profilePicture?.url}
          />
        </div>
      </div>
    </PageLayout>
  );
}

export default CustomerChatPage;
