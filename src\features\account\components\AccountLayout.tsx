import LogoutModal from "@/components/ui/logoutModal";
import { useBreadcrumbStore } from "@/store/breadcrumStore";
import React, { useState } from "react";
import { Link, useLocation } from "react-router-dom";

// Navigation items for the account sidebar
const ACCOUNT_NAV_ITEMS = [
  { name: "Basic details", href: "/account/basic-details" },
  { name: "My orders", href: "/account/orders" },
  { name: "Services booked", href: "/account/services" },
  { name: "Scheduled calls", href: "/account/scheduled-calls" },
  { name: "Care plan", href: "/account/care-plan" },
];

function AccountLayout({ children }: { children: React.ReactNode }) {
  const [isOpen, setIsOpen] = useState(false);
  const onClose = () => setIsOpen(false);
  const onOpen = () => setIsOpen(true);
  const { getCrumbByHref } = useBreadcrumbStore();

  const location = useLocation();
  const currentPath = location.pathname;
  const activeSection = getCrumbByHref(currentPath);

  return (
    <>
      <div className="container w-11/12 mx-auto">
        <div className="flex flex-col md:gap-8 md:mt-4 md:flex-row">
          {/* Sidebar Navigation - Hidden on mobile when a section is active */}
          <aside
            className={`w-full border border-t-0 border-b-0 border-l-0 md:w-64 shrink-0 border-gray-2 ${
              activeSection?.href !== "/account" ? "hidden md:block" : "block"
            }`}
          >
            <div className="">
              <div>
                <h2 className="mb-6 text-lg font-semibold">My account</h2>
                <nav className="flex flex-col space-y-2">
                  {ACCOUNT_NAV_ITEMS.map((item) => (
                    <Link
                      key={item.name}
                      to={item.href}
                      className={`py-2 px-3 rounded-md transition-colors ${
                        currentPath.split("/").includes(item.href.split("/")[2])
                          ? "text-black font-semibold"
                          : "text-gray-600 hover:bg-gray-50"
                      }`}
                    >
                      {item.name}
                    </Link>
                  ))}
                </nav>
              </div>
              <button
                onClick={onOpen}
                className="block px-3 py-2 mx-0 mt-5 transition-colors text-error hover:text-red-600"
              >
                Logout
              </button>
            </div>
          </aside>

          {/* Main Content - Only shown when a section is active on mobile, always shown on desktop */}
          <main
            className={`flex-1 bg-white rounded-md ${
              !activeSection ? "hidden md:block" : "block"
            }`}
          >
            {children}
          </main>
        </div>
      </div>
      <LogoutModal isOpen={isOpen} onClose={onClose} />
    </>
  );
}

export default AccountLayout;
