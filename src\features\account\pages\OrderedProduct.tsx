import { useParams } from "react-router-dom";
import AccountLayoutWrapper from "../components/AccountLayoutWrapper";
import OrderCard from "../components/OrderCard";
import ShippedAddress from "../components/ShippedAddress";
import Summary from "../components/Summary";
import ReviewRequest from "../components/ReviewRequest";
import OrderStatusSection from "../components/OrderStatusSection";
import { fetchOrderedProductApi } from "../api";
import { useQuery } from "@tanstack/react-query";
import OrderCardSkelton from "../components/OrderCardSkelton";
import SummarySkelton from "../components/SummarySkelton";
import ShippedAddressSkelton from "../components/ShippedAddressSkelton";
import { OrderItems } from "../type";
import { useOrderStore } from "@/store/orderStore";
import { useEffect } from "react";
function OrderedProduct() {
  const { id } = useParams();
  const { setFetchOrders } = useOrderStore();
  const {
    data,
    isPending: loading,
    refetch,
  } = useQuery({
    queryKey: ["ordered-product", id],
    queryFn: () => fetchOrderedProductApi(id as string),
  });
  useEffect(() => {
    setFetchOrders(refetch);
  }, [refetch]);
  const formatedOrder = {
    _id: data?.data?.order?._id,
    product: {
      title: data?.data?.order?.product?.title,
      images: data?.data?.order?.product?.images,
    },
    status: data?.data?.order?.status[0],
    hasRequestedReview: data?.data?.order?.hasRequestedReview,
    companyName: data?.data?.order?.companyName,
    category: data?.data?.order?.category,
  };
  let itemCancelled = false;
  if (
    data?.data?.order?.status[data?.data?.order?.status.length - 1].status ===
    "item cancelled"
  ) {
    itemCancelled = true;
  }

  return (
    <AccountLayoutWrapper>
      <div className="grid h-full grid-cols-12 gap-x-5 gap-y-4 lg:gap-y-0 ">
        <div className="flex flex-col col-span-12 lg:col-span-8 gap-y-4">
          {loading ? (
            <div className="space-y-4">
              {[1, 2].map((_, index) => (
                <OrderCardSkelton key={index} />
              ))}
            </div>
          ) : (
            <OrderCard
              itemCancelled={itemCancelled}
              order={formatedOrder as OrderItems}
              hideDetail={true}
            />
          )}
          {data?.data?.order?.hasRequestedReview &&
            !data?.data?.order?.reviewAdded && (
              <ReviewRequest productId={data?.data?.order?.product?._id} />
            )}
          <OrderStatusSection
            sellerId={data?.data?.order?.product?.sellerId}
            orderProgress={data?.data?.order?.status || []}
          />
        </div>
        <div className="flex flex-col h-full col-span-12 lg:col-span-4 gap-y-4">
          {loading ? (
            <ShippedAddressSkelton />
          ) : (
            <ShippedAddress
              address={data?.data?.order?.shippingAddress || ""}
            />
          )}
          {loading ? (
            <SummarySkelton />
          ) : (
            <Summary
              subTotal={data?.data?.order?.subTotal}
              amountPaid={data?.data?.order?.amountPaid}
              discount={data?.data?.order?.couponDiscountAmount}
            />
          )}
        </div>
      </div>
    </AccountLayoutWrapper>
  );
}

export default OrderedProduct;
