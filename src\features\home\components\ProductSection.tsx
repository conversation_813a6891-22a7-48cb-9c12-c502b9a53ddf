import ProductCard from "../../../components/ui/product-card";
import Buttonwithicon from "@/components/ui/buttonwithicon";
import { useQuery } from "@tanstack/react-query";
import { fetchProductssApi } from "../api";
import { Product } from "../types";
import ProductSkelton from "@/features/products/components/ProductSkelton";
import { useRef } from "react";

function ProductSection() {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const { data, isPending: loading } = useQuery({
    queryKey: ["products"],
    queryFn: () => fetchProductssApi({}),
  });

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: -300, behavior: "smooth" });
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: 300, behavior: "smooth" });
    }
  };

  return (
    <section className="w-11/12 mx-auto mt-5 max-w-7xl">
      {/* Header section */}
      <div className="mb-6 md:mb-8">
        <div className="mb-2 text-xs font-medium tracking-wider uppercase text-orange-1 md:text-base">
          RECOMMENDED PRODUCTS
        </div>

        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <h2 className="max-w-xl mb-4 text-xl font-medium md:text-4xl md:mb-0">
            Safe and effective products, designed for your changing needs every
            month
          </h2>

          <Buttonwithicon variant="default" text="See all" href="/shop" />
        </div>
      </div>

      {/* Products - horizontally scrollable with arrow controls */}
      <div className="relative">
        {/* Left Arrow */}
        {!loading && data?.data?.products?.length>4 ? (
          <button
            onClick={scrollLeft}
            className="absolute left-0 z-10 hidden p-2 transition-all -translate-y-1/2 rounded-full shadow-md cursor-pointer lg:block top-1/2 bg-white/80 hover:bg-white hover:shadow-lg"
            aria-label="Scroll left"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-orange-1"
            >
              <path d="M15 18l-6-6 6-6" />
            </svg>
          </button>
        ) : null}

        {/* Scrollable Container */}
        <div
          ref={scrollContainerRef}
          className="overflow-x-auto scrollbar-hide"
        >
          <div className="flex space-x-4">
            {loading
              ? [1, 2, 3, 4, 5, 6].map((_, i) => (
                  <ProductSkelton
                    className="min-w-[300px] border p-4 rounded-lg border-neutral-40 max-w-[300px] md:max-w-[300px] flex-shrink-0"
                    key={i}
                  />
                ))
              : data?.data?.products?.map((product: Product) => (
                  <div
                    key={product._id}
                    className="min-w-[300px] border p-4 rounded-lg border-neutral-40 max-w-[300px] md:max-w-[300px] flex-shrink-0"
                  >
                    <ProductCard
                      id={product._id}
                      image={product.images[0]}
                      title={product.title}
                      brand={product.seller.companyName}
                      price={product.price}
                      rating={product.totalRating}
                    />
                  </div>
                ))}
          </div>
        </div>

        {/* Right Arrow */}
        {!loading && data?.data?.products?.length >4? (
          <button
            onClick={scrollRight}
            className="absolute right-0 z-10 p-2 transition-all -translate-y-1/2 rounded-full shadow-md cursor-pointer hsidden lg:block top-1/2 bg-white/80 hover:bg-white hover:shadow-lg"
            aria-label="Scroll right"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-orange-1"
            >
              <path d="M9 18l6-6-6-6" />
            </svg>
          </button>
        ) : null}
      </div>
    </section>
  );
}

export default ProductSection;
