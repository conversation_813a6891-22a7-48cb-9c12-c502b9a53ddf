import { Modal } from "@/components/ui/modal";
import CloseIcon from "@/assets/close-icon.png";

function RefundModal({
  isOpen,
  onClose,
  refundPolicy,
}: {
  isOpen: boolean;
  onClose: () => void;
  refundPolicy: string;
}) {
  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <div>
        <div className="flex justify-end">
          <img
            onClick={onClose}
            className="cursor-pointer"
            src={CloseIcon}
            alt="close"
          />
        </div>
        <div className="p-3">
          <h1 className="mb-5 text-xl font-semibold">Refund Policy</h1>
          <p className="text-neutral-300">{refundPolicy}</p>
        </div>
      </div>
    </Modal>
  );
}

export default RefundModal;
