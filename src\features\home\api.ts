import api from "@/lib/axios";
import { CarePlanCardItem, ProductQueryParams } from "./types";

export const fetchProductssApi = async (params: ProductQueryParams) => {
  const { data } = await api.get("/api/v1/product", { params });
  return data;
};

export const fetchCarePlanApi = async (): Promise<CarePlanCardItem[]> => {
  const { data } = await api.get("/api/v1/users/customer/care-plan");
  return data?.data?.carePlan;
};
