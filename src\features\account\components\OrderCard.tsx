import { Link, useNavigate } from "react-router-dom";
import Buttonwithicon from "@/components/ui/buttonwithicon";
import ChatIcon from "@/assets/orange-chat-icon.svg";
import { OrderItems, Orderstatus } from "../type";
import OrderStatus from "./OrderStatus";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import ReviewModal from "./ReviewModal";

function OrderCard({
  order,
  hideDetail,
  itemCancelled,
}: {
  order: OrderItems;
  hideDetail?: boolean;
  itemCancelled?: boolean;
}) {
  const navigate = useNavigate();
  const [isOpen, setIsOpen] = useState(false);
  const onClose = () => setIsOpen(false);
  const onOpen = () => setIsOpen(true);
  const showChatButton = () => {
    if (itemCancelled) return false;
    if (order.status.status === "item cancelled") return false;
    return true;
  };
  const showReviewButton = () => {
    return order.hasRequestedReview && !order.reviewAdded && !hideDetail;
  };
  const handleChatClick = () => {
    navigate("/chat", {
      state: {
        receiverId: order.product.sellerId,
        name: order.companyName,
        description: order.category,
      },
    });
  };
  return (
    <>
      <div
        key={order._id}
        className=" md:min-h-[120px] p-5 overflow-hidden border rounded-lg border-gray-2"
      >
        <div className="flex flex-col justify-between md:flex-row md:items-center">
          <div className="flex gap-x-3">
            <div className="flex-shrink-0 w-20 h-20">
              <img
                src={order?.product?.images[0].image}
                alt={order?.product?.title}
                className={` ${
                  order.status.status === "item cancelled" ? "grayscale" : ""
                } object-cover w-full h-full rounded-md `}
              />
            </div>
            <div className="flex flex-col h-20 md:justify-between gap-y-1 md:gap-y-0">
              <h3 className="text-lg font-medium">{order?.product?.title}</h3>
              <OrderStatus
                status={order.status.status as Orderstatus}
                date={order.status.date}
              />
            </div>
          </div>

          <div className="flex flex-col justify-between md:h-20">
            <div className={`${!hideDetail ? "hidden" : "mt-4"} md:block`}>
              {showReviewButton() ? (
                <Button onClick={onOpen}>Write a reivew</Button>
              ) : showChatButton() ? (
                <Buttonwithicon
                  onClick={handleChatClick}
                  text="Contact Seller"
                  href="/chat"
                  variant="outline"
                  icon={ChatIcon}
                  classname="w-full text-center"
                />
              ) : null}
            </div>
            {!hideDetail && (
              <Link
                to={`/account/orders/${order._id}`}
                className="text-sm text-end text-neutral-100 md:text-black"
              >
                view details
              </Link>
            )}
          </div>
        </div>
      </div>
      <ReviewModal
        productId={order.product._id}
        isOpen={isOpen}
        onClose={onClose}
      />
    </>
  );
}

export default OrderCard;
