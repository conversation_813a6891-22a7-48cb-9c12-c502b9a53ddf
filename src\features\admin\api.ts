import api from "@/lib/axios";
import {
  ApproveProviderApiPayload,
  ApproveSellerApiPayload,
  LoginPayload,
  ProductApiResponse,
  SellerApiPayload,
  SellerApiResponse,
  SellerProductsResponse,
} from "./type";

export const adminLoginApi = async (payload: LoginPayload) => {
  const { data } = await api.post("/api/v1/auth/admin/login", payload);
  return data;
};
export const fetchSellersApi = async (params: SellerApiPayload) => {
  const { data } = await api.get("/api/v1/users/admin/sellers", { params });
  return data;
};
export const fetchSellerApi = async (
  sellerId: string
): Promise<SellerApiResponse> => {
  const { data } = await api.get(`/api/v1/users/seller/${sellerId}`);
  return data?.data;
};
export const fetchSellerProductsApi = async (
  sellerId: string,
  page: number
): Promise<SellerProductsResponse> => {
  const { data } = await api.get(`/api/v1/users/seller/${sellerId}/products`, {
    params: { page },
  });
  return data?.data;
};

export const fetchProductDetailsApi = async (
  productId: string
): Promise<ProductApiResponse> => {
  const { data } = await api.get(`api/v1/product/${productId}`);
  return data?.data?.product;
};

export const fetchProvidersApi = async (params: SellerApiPayload) => {
  const { data } = await api.get("/api/v1/users/admin/providers", { params });
  return data;
};

export const ApproveSellersApi = async (payload: ApproveSellerApiPayload) => {
  const { data } = await api.patch(
    "/api/v1/users/admin/seller/verify",
    payload
  );
  return data;
};

export const ApproveProviderApi = async (
  payload: ApproveProviderApiPayload
) => {
  const { data } = await api.patch(
    "/api/v1/users/admin/provider/verify",
    payload
  );
  return data;
};
