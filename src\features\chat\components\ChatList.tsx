import RecentChatHeader from "./RecentChatHeader";

function ChatList({
  selectedChatId,
  onChatSelect,
  handleHideMessageSection,
}: {
  selectedChatId: string | null;
  onChatSelect: (chatId: string) => void;
  handleHideMessageSection: () => void;
}) {
  const handleChatSelect = (chatId: string) => {
    onChatSelect(chatId);
    handleHideMessageSection();
  };
  return (
    <div className="w-full max-h-[80vh] overflow-y-scroll scrollbar-hide ">
      {[1, 2, 3, 4, 5,6,7,44,33,43,12].map((_, index) => (
        <div
          className={`${selectedChatId === index.toString() ? "bg-tints-40" : ""}`}
          key={index}
          onClick={() => handleChatSelect(index.toString())}
        >
          <RecentChatHeader
            // image="https://nurture-ye-s3.s3.us-east-1.amazonaws.com/public/nurture-images/1747977307621.png"
            name="Seller"
            lastMessage="Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.
        
"
            unreadCount={1}
          />
        </div>
      ))}
    </div>
  );
}

export default ChatList;
