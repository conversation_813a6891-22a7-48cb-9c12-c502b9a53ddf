import Nurture<PERSON>ogo from "@/assets/nurture-logo.png";
import SellerDashboardLogo from "@/assets/seller-dashboard.png";
import MessageLogo from "@/assets/message.png";
import LogoutLogo from "@/assets/logout.png";
import ReviewLogo from "@/assets/review.png";
import ServiceLogo from "@/assets/service-icon.png";
import ProfileLogo from "@/assets/profile-icon.png";

import { Link, useLocation } from "react-router-dom";
import LogoutModal from "@/components/ui/logout-modal";
import { useState } from "react";

function Navbar() {
  const [isOpen, setIsOpen] = useState(false);
  const onClose = () => {
    setIsOpen(false);
  };
  const onOpen = () => {
    setIsOpen(true);
  };

  const navbarItems = [
    { icon: SellerDashboardLogo, name: "Dashboard", href: "/provider/dashboard" },
    { icon: ServiceLogo, name: "Services", href: "/provider/services" },
    { icon: Message<PERSON>ogo, name: "Messages", href: "/provider/messages" },
    { icon: ReviewLogo, name: "Reviews", href: "/provider/reviews" },
  ];

  const navBarBottomItems = [
    {
      icon: ProfileLogo,
      name: "Profile",
      href: "/provider/profile"
    },
  ];

  const { pathname } = useLocation();

  // Extract the key from the pathname for active state detection
  let key = pathname.split("/")[2];

  // Special handling for dashboard-preview route
  if (key === "dashboard-preview") {
    key = "dashboard";
  }

  console.log("Current path:", pathname);
  console.log("Active key:", key);

  // Log each menu item for debugging
  navbarItems.forEach(item => {
    console.log(`Menu item: ${item.name}, toLocaleLowerCase: ${item.name.toLocaleLowerCase()}, Match: ${key === item.name.toLocaleLowerCase()}`);
  });

  return (
    <>
      <aside className="flex flex-col justify-between h-full p-5 gap-y-5">
        <div className="p-3">
          <img src={NurtureLogo} alt="nurture" className="mx-auto" />
        </div>
        <div className="grow">
          <ul>
            {navbarItems.map((item) => (
              <Link
                to={item.href}
                key={item.name}
                className="my-5 cursor-pointer"
              >
                <li
                  className={`${
                    (key === item.name.toLocaleLowerCase() ||
                     (key === "dashboard-preview" && item.name.toLocaleLowerCase() === "dashboard"))
                     ? "bg-tints-200" : ""
                  } flex gap-x-3 my-5`}
                  key={item.name}
                >
                  <div
                    className={`w-1 ${
                      (key === item.name.toLocaleLowerCase() ||
                       (key === "dashboard-preview" && item.name.toLocaleLowerCase() === "dashboard"))
                        ? "bg-orange-1"
                        : "bg-transparent"
                    }`}
                  ></div>

                  <div className="flex py-2 my-auto gap-x-3">
                    {typeof item.icon === 'string' ? (
                      <img className="my-auto" src={item.icon} alt={item.name} />
                    ) : (
                      <span className="my-auto text-gray-700">{item.icon}</span>
                    )}
                    <span
                      className={`${
                        (key === item.name.toLocaleLowerCase() ||
                         (key === "dashboard-preview" && item.name.toLocaleLowerCase() === "dashboard"))
                          ? "font-semibold"
                          : "font-normal"
                      }`}
                    >
                      {item.name}
                    </span>
                  </div>
                </li>
              </Link>
            ))}
          </ul>
        </div>
        <div className="flex flex-col gap-y-8">
          {navBarBottomItems.map((item) => (
            <Link
              to={item.href}
              key={item.name}
              className="flex ml-5 cursor-pointer gap-x-3"
            >
              <img className="my-auto" src={item.icon} alt={item.name} />
              <p className={
                (key === item.name.toLocaleLowerCase() ||
                (key === "dashboard-preview" && item.name.toLocaleLowerCase() === "dashboard"))
                ? "font-semibold" : "font-normal"
              }>
                {item.name}
              </p>
            </Link>
          ))}
          <div className="flex ml-5 cursor-pointer gap-x-3" onClick={onOpen}>
            <img className="my-auto" src={LogoutLogo} alt="logout" />
            <button>Logout</button>
          </div>
        </div>
      </aside>
      <LogoutModal isOpen={isOpen} onClose={onClose} />
    </>
  );
}

export default Navbar;
