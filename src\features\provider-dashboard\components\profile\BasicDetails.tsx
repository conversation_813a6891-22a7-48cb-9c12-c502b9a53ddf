import { useState } from "react";
import ProfileHeader from "./ProfileHeader";
import IntroductoryVideo from "./IntroductoryVideo";
import Certifications from "./Certifications";
import RefundPolicy from "./RefundPolicy";
import ServiceHighlights from "./ServiceHighlights";
import Photos from "./Photos";
import EditProfileDialog, { EditProfileFormData } from "./EditProfileDialog";

// Mock data for demonstration
const mockProvider = {
  name: "<PERSON>",
  rating: 4.5,
  description: "Specialist in New Mom Feeding",
  location: "Virtual",
  phone: "(*************",
  experience: "3 yrs exp",
  image: "https://randomuser.me/api/portraits/women/44.jpg",
  videoUrl: "https://www.w3schools.com/html/mov_bbb.mp4",
  refundPolicy: "Lorem ipsum dolor sit amet consectetur. Elit pulvinar sed lorem et justo sagittis. Habitasse molestiae tellus gravida dui. Pellentesque vel non bibendum et vel sit. Pharetra fuis odio turpis sagittis. Volutatis diam commodo. Lorem ipsum dolor sit amet consectetur. Elit pulvinar sed lorem et justo sagittis. Habitasse molestiae tellus gravida dui. Pellentesque vel non bibendum et vel sit. Pharetra fuis odio turpis sagittis. Volutatis diam commodo.",
  certifications: [
    { id: "1", name: "Certificate.pdf", file: "#" },
    { id: "2", name: "Certificate.pdf", file: "#" }
  ],
  serviceHighlights: [
    { id: "1", text: "Diapering, bathing, and dressing" },
    { id: "2", text: "Safe sleep practices and soothing techniques" },
    { id: "3", text: "Feeding support (breastfeeding/bottle-feeding)" },
    { id: "4", text: "Providing a supportive and nurturing environment" },
    { id: "5", text: "Nighttime support and meal preparation" },
    { id: "6", text: "Postpartum wound care" }
  ],
  photos: [
    { id: "1", url: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" },
    { id: "2", url: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" },
    { id: "3", url: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" },
    { id: "4", url: "https://images.unsplash.com/photo-1517841905240-472988babdf9?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" }
  ]
};

function BasicDetails() {
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  const handleEditProfile = () => {
    setIsEditDialogOpen(true);
  };

  const handleSaveProfile = (data: EditProfileFormData) => {
    console.log("Saving profile data:", data);
    // TODO: Integrate with API when you provide the API endpoint
    setIsEditDialogOpen(false);
  };

  return (
    <div className="space-y-8 py-8" >
      <ProfileHeader
        name={mockProvider.name}
        rating={mockProvider.rating}
        description={mockProvider.description}
        location={mockProvider.location}
        phone={mockProvider.phone}
        experience={mockProvider.experience}
        image={mockProvider.image}
        onEditClick={handleEditProfile}
      />

      <div className="">
        <p className="text-neutral-600">{mockProvider.refundPolicy}</p>
      </div>

      <IntroductoryVideo videoUrl={mockProvider.videoUrl} />

      <Certifications certifications={mockProvider.certifications} />

      <RefundPolicy policy={mockProvider.refundPolicy} />

      <ServiceHighlights highlights={mockProvider.serviceHighlights} />

      <Photos photos={mockProvider.photos} />

      {/* Edit Profile Dialog */}
      <EditProfileDialog
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        onSubmit={handleSaveProfile}
      />
    </div>
  );
}

export default BasicDetails;
