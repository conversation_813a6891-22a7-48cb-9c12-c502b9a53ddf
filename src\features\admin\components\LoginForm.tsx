import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useState } from "react";
import FingerPrintImage from "@/assets/fingerprint.png";
import { useNavigate } from "react-router-dom";
import { LuEye } from "react-icons/lu";
import { PiEyeSlash } from "react-icons/pi";
import { useForm } from "react-hook-form";
import { joiResolver } from "@hookform/resolvers/joi";
import { useMutation } from "@tanstack/react-query";
import { AxiosError } from "axios";

import { LoginPayload } from "../type";
import { adminLoginApi } from "../api";
import { LoginSchema } from "../validation";
import { useAuthStore } from "@/store/authStore";
import { showToast } from "@/lib/toast";
function LoginForm() {
  const navigate = useNavigate();
  const login = useAuthStore((state) => state.login);
  const [showPassword, setShowPassword] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginPayload>({
    resolver: joiResolver(LoginSchema),
  });

  const { mutate, isPending } = useMutation({
    mutationFn: adminLoginApi,
    onSuccess: ({ data }) => {
      console.log(data,'login data')
      login({ accessToken: data.accessToken, role: "admin" });
      navigate("/admin/sellers");
    },
    onError: (error: AxiosError) => {
      if (error.response?.status === 401) {
        showToast("Invalid email or password", "error");
      } else {
        showToast("Something went wrong", "error");
      }
    },
  });

  const onSubmit = (data: LoginPayload) => {
    mutate(data);
  };

  return (
    <div className="w-full max-w-md space-y-6 text-center">
      <div className="space-y-2">
        <img
          src={FingerPrintImage}
          alt="Fingerprint"
          className="mx-auto object-cover"
        />
        <h4 className="text-neutral-300">Admin Login</h4>
        <h1 className="text-3xl md:text-4xl font-bold font-prettywise">
          Welcome Back
        </h1>
      </div>

      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="space-y-4 text-left">
          {/* Email Input */}
          <div>
            <Input
              className="border-input-border focus:border-slate-300"
              placeholder="Email"
              type="email"
              {...register("email")}
            />
            {errors.email && (
              <p className="text-red-500 text-sm">{errors.email.message}</p>
            )}
          </div>

          {/* Password Input */}
          <div>
            <div className="border rounded-sm border-input-border focus:border-slate-300 flex">
              <Input
                className="border-none"
                placeholder="Password"
                type={showPassword ? "text" : "password"}
                {...register("password")}
              />
              {showPassword ? (
                <PiEyeSlash
                  onClick={() => setShowPassword(false)}
                  className="text-[#827C7A] my-auto mr-2 hover:cursor-pointer"
                  size={25}
                />
              ) : (
                <LuEye
                  onClick={() => setShowPassword(true)}
                  className="text-[#827C7A] my-auto mr-2 hover:cursor-pointer"
                  size={25}
                />
              )}
            </div>
            {errors.password && (
              <p className="text-red-500 text-sm">{errors.password.message}</p>
            )}
          </div>
        </div>

        <div className="space-y-2 mt-6 md:mt-12 flex flex-col gap-y-3 md:gap-y-1">
          <Button
            type="submit"
            className="w-full p-2 hover:bg-[#c65a3c]"
            disabled={isPending}
          >
            {isPending ? "Logging in..." : "Login"}
          </Button>
        </div>
      </form>
    </div>
  );
}

export default LoginForm;
