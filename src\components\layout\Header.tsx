import { useCallback, memo, useEffect, useState } from "react";
import { Link } from "react-router-dom";

import Header<PERSON>ogo from "@/assets/header-logo.png";
import NotificationIcon from "@/assets/notification.png";
import ChatIcon from "@/assets/chat.png";
import ProfileIcon from "@/assets/profile-icon.png";
import CartIcon from "@/assets/cart.png";
import SearchIcon from "@/assets/search.png";
import MenuIcon from "@/assets/menu-icon.png";
import RightArrowIcon from "@/assets/arrow-up-orange-icon.png";

import { Input } from "../ui/input";
import { Button } from "../ui/button";
import MenuDrawer from "@/features/home/<USER>/MenuDrawer";
import { useCartStore } from "@/store/cartStore";
import { useAuthStore } from "@/store/authStore";
import { useQuery } from "@tanstack/react-query";
import { fetchProductAndServiceApi, fetchRecentSearchApi } from "@/api";
import { navigateLoggedInUser } from "@/lib/utils";
// import { debounce } from "lodash";

type HeaderProps = {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
};

const NAVIGATION_ITEMS = [
  { name: "Shop", href: "/shop" },
  { name: "Services", href: "/" },
  { name: "Resources", href: "/" },
];

const Header = ({ open, setOpen }: HeaderProps) => {
  const { user, setAccesstoken } = useAuthStore((state) => state);
  const [toggleRecentSearch, setToggleRecentSearch] = useState(false);
  const [search, setSearch] = useState("");

  const onClose = useCallback(() => {
    setOpen(false);
  }, [setOpen]);

  const { cartCount, fetchCartCount } = useCartStore();
  const nurtureUser = localStorage.getItem("nurtureUser");
  const handleCartcount = async () => {
    if (nurtureUser === "customer" && !user?.accessToken) {
      const token = await setAccesstoken();
      if (token) {
        await fetchCartCount();
      }
      return;
    }
    await fetchCartCount();
  };
  useEffect(() => {
    if (nurtureUser === "customer") {
      handleCartcount();
    }
  }, [fetchCartCount, nurtureUser]);

  const openRecentSearch = (e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();
    setToggleRecentSearch(false); // hiding feature for now, will be display after api integration
  };
  const closeRecentSearch = () => {
    setToggleRecentSearch(false);
  };
  const recentSearchResult = useQuery({
    queryKey: ["recentSearch"],
    queryFn: fetchRecentSearchApi,
  });
  const searchResult = useQuery({
    queryKey: ["search"],
    queryFn: () => fetchProductAndServiceApi(search),
    enabled: false,
  });

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value);
    searchResult.refetch();
  };
  const productsAndServices =
    searchResult?.data?.data || recentSearchResult?.data?.data;

  const renderNavigation = () => (
    <nav className="hidden space-x-8 md:flex">
      {NAVIGATION_ITEMS.map(({ name, href }) => (
        <Link
          key={name}
          to={href}
          className="my-auto text-gray-700 hover:text-gray-900"
        >
          {name}
        </Link>
      ))}
    </nav>
  );

  const renderSearchBar = () => (
    <div className="flex-1 md:w-full md:max-w-md ">
      <div
        onClick={openRecentSearch}
        className="rounded-full  flex mx-auto border border-[#C5C2C2] focus:outline-none focus:ring-1 focus:ring-gray-300"
      >
        <img src={SearchIcon} alt="Search" className="mx-3 my-auto" />
        <Input
          onChange={handleSearch}
          type="text"
          placeholder="Search products or services"
          className="border-0 rounded-full"
        />
      </div>
    </div>
  );

  const renderRecentSearch = () => {
    return (
      <div className="relative flex flex-col p-4 bg-white border rounded-lg shadow-xl border-gray-50">
        <h1 className="text-sm text-neutral-300">Products</h1>
        <div className="flex flex-col mt-3 gap-y-3">
          {!productsAndServices?.products.length ? (
            <div className="text-neutral-300">No Results.</div>
          ) : (
            productsAndServices?.products.map((product) => {
              return (
                <Link to={`/product/${product._id}`}>
                  <div className="flex justify-between cursor-pointer">
                    <p className="text-gray-700 hover:text-gray-900 ">
                      {product.title}
                    </p>
                    <img src={RightArrowIcon} alt="arrow" className="my-auto" />
                  </div>
                </Link>
              );
            })
          )}
        </div>
        <div className="mt-10">
          <h1 className="text-sm text-neutral-300">Services</h1>
          <div className="flex flex-col mt-3 gap-y-3">
            {!productsAndServices?.services.length ? (
              <div className="text-neutral-300">No Results.</div>
            ) : (
              productsAndServices?.services?.map((service) => {
                return (
                  <Link to={`/service/${service._id}`}>
                    <div className="flex justify-between cursor-pointer">
                      <p className="text-gray-700 hover:text-gray-900 ">
                        {service.title}
                      </p>
                      <img
                        src={RightArrowIcon}
                        alt="arrow"
                        className="my-auto"
                      />
                    </div>
                  </Link>
                );
              })
            )}
          </div>
        </div>
      </div>
    );
  };
  const renderActions = () => (
    <div className="flex items-center w-1/2 justify-evenly">
      <img
        src={NotificationIcon}
        alt="Notifications"
        className="text-gray-700 hover:text-gray-900"
      />
      <img
        src={ChatIcon}
        alt="Chat"
        className="text-gray-700 hover:text-gray-900"
      />
      <Link to={"/cart"} className="relative">
        <img
          src={CartIcon}
          alt="Cart"
          className="text-gray-700 hover:text-gray-900"
        />
        {cartCount > 0 && (
          <div className="absolute flex items-center justify-center w-5 h-5 text-xs font-bold text-white rounded-full bg-orange-1 -top-2 -right-2">
            {cartCount}
          </div>
        )}
      </Link>
      {nurtureUser ? (
        <Link to={navigateLoggedInUser()}>
          <img
            src={ProfileIcon}
            alt="Profile"
            className="text-gray-700 hover:text-gray-900"
          />
        </Link>
      ) : (
        <Link to="/register">
          <Button>Sign up</Button>
        </Link>
      )}
    </div>
  );

  return (
    <header onClick={closeRecentSearch} className="border-b border-gray-100">
      <div className="hidden px-4 mx-auto my-4 md:block">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div className="flex w-1/3 justify-evenly">
            <Link to="/">
              <img src={HeaderLogo} alt="Header Logo" className="h-9" />
            </Link>
            {renderNavigation()}
          </div>
          <div className="flex flex-col justify-between w-full md:flex-row md:w-1/2">
            {renderSearchBar()}
            {toggleRecentSearch && (
              <div className="absolute z-10 border rounded-lg border-gray-50 top-20 md:w-full md:max-w-md">
                {renderRecentSearch()}
              </div>
            )}
            {renderActions()}
          </div>
        </div>
      </div>

      {/* Mobile UI */}
      {!open ? (
        <div className="m-4 space-y-3 md:hidden">
          <div className="flex justify-between">
            <Link to="/" className="my-auto">
              <img src={HeaderLogo} alt="Header Logo" className="h-9" />
            </Link>
            <div className="flex gap-x-5">
              <img
                src={NotificationIcon}
                alt="Notifications"
                className="my-auto"
              />
              <Link to={"/cart"} className="relative my-auto">
                <img src={CartIcon} alt="Cart" className="my-auto" />
                {cartCount > 0 && (
                  <div className="absolute flex items-center justify-center w-5 h-5 text-xs font-bold text-white rounded-full bg-orange-1 -top-2 -right-2">
                    {cartCount}
                  </div>
                )}
              </Link>
              <img
                src={MenuIcon}
                alt="Menu"
                className="my-auto"
                onClick={() => setOpen(true)}
              />
            </div>
          </div>
          {renderSearchBar()}
          {toggleRecentSearch && (
            <div className="absolute z-10 w-10/12 border rounded-lg border-gray-50 top-30 md:w-full md:max-w-md">
              {renderRecentSearch()}
            </div>
          )}
        </div>
      ) : (
        <MenuDrawer open={open} onClose={onClose} />
      )}
    </header>
  );
};

export default memo(Header);
