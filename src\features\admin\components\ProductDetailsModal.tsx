import { Modal } from "@/components/ui/modal";
import CloseIcon from "@/assets/close-icon.svg";
import GoBackIcon from "@/assets/go-back.svg";
import { useQuery } from "@tanstack/react-query";
import { fetchProductDetailsApi } from "../api";
import ProductDetailSkelton from "./ProductDetailSkelton";
function ProductDetailsModal({
  isOpen,
  onClose,
  openSellerProducts,
  selectedProductId,
}: {
  isOpen: boolean;
  onClose: () => void;
  openSellerProducts: () => void;
  selectedProductId: string;
}) {
  const handleGoBack = () => {
    openSellerProducts();
    onClose();
  };
  const { data, isPending: loading } = useQuery({
    queryKey: ["product-details", selectedProductId],
    queryFn: () => fetchProductDetailsApi(selectedProductId),
    enabled: !!selectedProductId && isOpen,
  });
  console.log("Product Details Data:", data);
  return (
    <Modal className="lg:min-w-[650px]" isOpen={isOpen} onClose={onClose}>
      <div>
        <div className="flex items-center justify-between mb-6">
          <div className="flex cursor-pointer gap-x-4" onClick={handleGoBack}>
            <img
              className="transition-opacity hover:opacity-70"
              src={GoBackIcon}
              alt="go back"
            />
            <h1 className="text-gray-900 ">Go back</h1>
          </div>
          <img
            onClick={onClose}
            className="transition-opacity cursor-pointer hover:opacity-70"
            src={CloseIcon}
            alt="close"
          />
        </div>
        {loading ? (
          <ProductDetailSkelton />
        ) : (
          <div className="mt-8">
            <div className="flex flex-col justify-between p-3 border rounded-lg cursor-pointer gap-y-5 border-tints-50">
              <div className="flex justify-between">
                <div className="flex gap-x-2">
                  <img
                    src={data?.images[0].image}
                    alt="product"
                    className="w-14 h-14"
                  />
                  <div>
                    <h1 className="text-lg font-semibold">{data?.title}</h1>
                    <div className="flex gap-x-3">
                      <span>
                        <span className="text-neutral-300">quantity:</span>{" "}
                        {data?.quantity}
                      </span>

                      <span>
                        <span className="text-neutral-300">status:</span>{" "}
                        {data?.stockStatus}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="my-auto">
                  <p className="my-auto text-lg font-semibold">
                    ${data?.price}
                  </p>
                </div>
              </div>

              <div className="flex mt-4 overflow-x-scroll gap-x-2 scrollbar-hide">
                {data?.images.map((image) => (
                  <img
                    key={image._id}
                    src={image.image}
                    alt="product"
                    className="w-16 h-16 rounded-lg"
                  />
                ))}
              </div>

              <div>
                <p className="mt-2 text-neutral-500 ">{data?.description}</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
}

export default ProductDetailsModal;
