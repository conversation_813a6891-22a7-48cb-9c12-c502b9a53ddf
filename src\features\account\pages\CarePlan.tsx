import AccountLayoutWrapper from "../components/AccountLayoutWrapper";
import fingerPrintLogo from "@/assets/fingerprint.png";
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import CarePlanCard from "../components/CarePlanCard";
import { fetchCarePlanApi } from "../api";
import { useQuery } from "@tanstack/react-query";
import { CarePlanCardItem } from "../type";
import CarePlanCardSkelton from "../components/CarePlanCardSkelton";

function CarePlan() {
  const { data, isPending: loading } = useQuery({
    queryKey: ["care-plan"],
    queryFn: fetchCarePlanApi,
  });

  return (
    <AccountLayoutWrapper>
      <div className="bg-white rounded-lg  border border-nuetral-40 p-6 max-h-[80vh] overflow-y-auto scrollbar-hide">
        {/* Header Section */}
        <div className="flex flex-col justify-between mb-6 md:flex-row">
          <div className="flex flex-col md:flex-row md:items-center">
            <img
              src={fingerPrintLogo}
              alt="nurture"
              className="w-12 h-12 mb-4 mr-4 md:mb-0"
            />
            <div>
              <h1 className="text-2xl font-bold text-gray-800">
                Your Personalized Care Plan
              </h1>
              <p className="text-gray-600">
                Tailored recommendations for your postpartum journey
              </p>
            </div>
          </div>
          <div className="mt-4 md:mt-0">
            <Button
              asChild
              variant="outline"
              className="flex items-center gap-2 hover:bg-orange-1 hover:text-white"
            >
              <Link to="/questionaire">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="lucide lucide-clipboard-list"
                >
                  <rect width="8" height="4" x="8" y="2" rx="1" ry="1" />
                  <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2" />
                  <path d="M12 11h4" />
                  <path d="M12 16h4" />
                  <path d="M8 11h.01" />
                  <path d="M8 16h.01" />
                </svg>
                Retake Questionnaire
              </Link>
            </Button>
          </div>
        </div>
        <div className="p-5 bg-white rounded-lg">
          {loading ? (
            [1, 2].map((_, i) => <CarePlanCardSkelton key={i} />)
          ) : !data?.length ? (
            <div className="py-10 text-center">
              <div className="mb-4 text-5xl text-gray-400">📋</div>
              <h3 className="text-lg font-medium text-gray-700">
                No care plan items yet
              </h3>
              <p className="text-gray-500">
                Your personalized care plan will appear here once it's ready.
              </p>
            </div>
          ) : (
            data?.map((plan: CarePlanCardItem) => (
              <div className="space-y-4 border-t ">
                <CarePlanCard key={plan.description} item={plan} />
              </div>
            ))
          )}
        </div>
      </div>
    </AccountLayoutWrapper>
  );
}

export default CarePlan;
