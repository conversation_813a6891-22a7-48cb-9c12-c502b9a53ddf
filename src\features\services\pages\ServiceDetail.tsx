import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import PageLayout from "@/components/layout/PageLayout";
import ServiceProviderInfo from "../components/ServiceProviderInfo";
import ServiceVideoPlayer from "../components/ServiceVideoPlayer";
import ServiceTabs from "../components/ServiceTabs";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { fetchProviderApi, fetchProviderServicesApi } from "../../admin/api";
import { ProviderApiResponse, Service } from "../../admin/type";

function ServiceDetail() {
  const { providerId } = useParams<{ providerId: string }>();
  const navigate = useNavigate();

  // State management
  const [provider, setProvider] = useState<ProviderApiResponse | null>(null);
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch provider and services data
  useEffect(() => {
    const fetchData = async () => {
      if (!providerId) {
        setError("Provider ID is required");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Fetch provider details and services in parallel
        const [providerResponse, servicesResponse] = await Promise.all([
          fetchProviderApi(providerId),
          fetchProviderServicesApi(providerId)
        ]);

        setProvider(providerResponse);
        setServices(servicesResponse.services);
      } catch (err) {
        console.error("Error fetching provider data:", err);
        setError("Failed to load provider information. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [providerId]);

  const handleGoBack = () => {
    navigate("/services");
  };

  if (loading) {
    return (
      <PageLayout>
        <div className="flex justify-center items-center h-64">
          <div className="text-lg">Loading provider information...</div>
        </div>
      </PageLayout>
    );
  }

  if (error || !provider) {
    return (
      <PageLayout>
        <div className="flex flex-col justify-center items-center h-64 gap-4">
          <div className="text-red-500 text-lg">{error || "Provider not found"}</div>
          <Button onClick={handleGoBack} variant="outline">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Services
          </Button>
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout>
      <div className="w-11/12 mx-auto mt-5 md:mt-2">
        {/* Go Back Button */}
        <Button
          onClick={handleGoBack}
          variant="ghost"
          className="mb-2 p-0 h-auto hover:text-gray-900"
        >
          <ArrowLeft className="w-4 h-4 mr-1" />
          Go back
        </Button>

        {/* Main Content Section */}
        <div className="flex flex-col lg:flex-row gap-8 mb-12">
          {/* Left Side - Provider Information */}
          <div className="w-full lg:w-2/3">
            <ServiceProviderInfo provider={provider.provider} />
          </div>

          {/* Right Side - Introductory Video */}
          <div className="w-full lg:w-1/3">
            <ServiceVideoPlayer
              videoUrl={provider.provider.introductionVideo?.url}
              providerName={`${provider.provider.firstName} ${provider.provider.lastName}`}
            />
          </div>
        </div>

        {/* Tabs Section */}
        <ServiceTabs
          services={services}
          provider={provider.provider}
        />
      </div>
    </PageLayout>
  );
}

export default ServiceDetail;
