import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import ProfileSetupStep1 from "./ProfileSetupStep1";
import ProfileSetupStep2 from "./ProfileSetupStep2";
import ProfileSetupStep3 from "./ProfileSetupStep3";
import FingerPrintImage from "@/assets/fingerprint.png";
import { Progress } from "@/components/ui/progress";


function ProfileSetupSteps() {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState<number>(1);
  const totalSteps = 3; // We now have 3 steps in this component (1, 2, 3)

  // Determine the initial step based on provider data
  useEffect(() => {
    // Check if we have a provider ID (which means basic details are completed)
    const providerId = localStorage.getItem("providerId");

    // Check if we're coming from a fresh registration (OTP verification)
    const isNewRegistration = sessionStorage.getItem("isNewRegistration") === "true";

    // If this is a new registration, always start at step 1
    if (isNewRegistration) {
      // Clear the flag after using it
      sessionStorage.removeItem("isNewRegistration");
      setCurrentStep(1);
      console.log("New registration: Starting at step 1: Basic details");
      return;
    }

    // For returning users, check completion status
    if (providerId) {
      // If we have a provider ID, check which steps are completed
      const hasAddedServices = localStorage.getItem("hasAddedServices") === "true";
      const hasSetAvailability = localStorage.getItem("hasSetAvailability") === "true";

      if (!hasAddedServices) {
        // If services are not added, go to step 2
        setCurrentStep(2);
        console.log("Returning user: Starting at step 2: Services");
      } else if (!hasSetAvailability) {
        // If availability is not set, go to step 3
        setCurrentStep(3);
        console.log("Returning user: Starting at step 3: Availability");
      }
    } else {
      // If no provider ID, start at step 1 (basic details)
      setCurrentStep(1);
      console.log("No provider ID: Starting at step 1: Basic details");
    }
  }, []);

  // Calculate progress percentage
  const progressPercentage = (currentStep / totalSteps) * 100;

  // Function to handle next step
  const handleNextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    } else if (currentStep === totalSteps) {
      // Navigate to the plan selection page (Step 4)
      navigate("/provider/setup-profile/plan-selection");
    }
  };

  // Function to handle previous step
  const handlePreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  return (
    <div
      className="w-full max-w-md text-center space-y-6 py-8"
    >
      <img
        src={FingerPrintImage}
        alt="Fingerprint"
        className="mx-auto object-cover"
      />
      <h1 className="text-3xl font-prettywise font-bold">
        Setup your profile
      </h1>

      {/* Progress Steps */}
      <div className="w-full">
        <div className="flex justify-between mb-2">
          <span className={currentStep >= 1 ? "text-orange-1 font-medium" : "text-gray-400"}>
            Basic details
          </span>
          <span className={currentStep >= 2 ? "text-orange-1 font-medium" : "text-gray-400"}>
            Services offered
          </span>
          <span className={currentStep >= 3 ? "text-orange-1 font-medium" : "text-gray-400"}>
            Availability
          </span>
        </div>
        <Progress value={progressPercentage} className="h-1" />
      </div>

      {/* Current Step Content */}
      <div className="w-full max-w-md">
        {currentStep === 1 && (
          <ProfileSetupStep1 onNext={handleNextStep} />
        )}
        {currentStep === 2 && (
          <ProfileSetupStep2
            onNext={handleNextStep}
            onBack={handlePreviousStep}
          />
        )}
        {currentStep === 3 && (
          <ProfileSetupStep3
            onNext={handleNextStep}
            onBack={handlePreviousStep}
          />
        )}
      </div>
    </div>
  );
}

export default ProfileSetupSteps;
