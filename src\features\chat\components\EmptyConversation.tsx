import GoBackIcon from "@/assets/go-back.svg";

function EmptyConversation({
  handleHideMessageSection,
}: {
  handleHideMessageSection: () => void;
}) {
  return (
    <div className="flex flex-col w-full h-full ">
      {/* Empty State Content */}
      <div className="flex flex-shrink-0 p-4 border-b gap-x-3 md:gap-0 border-nueutral-40">
        <img
          onClick={handleHideMessageSection}
          src={GoBackIcon}
          alt="go back"
          className="w-4 h-4 my-auto cursor-pointer md:hidden"
        />
      </div>
      <div className="flex items-center justify-center flex-1 min-h-0 my-20 md:my-0">
        <div className="max-w-md px-6 mx-auto text-center">
          {/* Chat Icon */}
          <div className="flex items-center justify-center w-20 h-20 mx-auto mb-6 bg-gray-100 rounded-full">
            <svg
              className="w-10 h-10 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
              />
            </svg>
          </div>

          {/* Main Message */}
          <h2 className="mb-3 text-xl font-semibold text-gray-900">
            Select a chat to start messaging
          </h2>

          {/* Description */}
          <p className="leading-relaxed text-gray-600">
            Choose a conversation from the sidebar to view messages and start
            chatting.
          </p>
        </div>
      </div>
    </div>
  );
}

export default EmptyConversation;
