import { But<PERSON> } from "@/components/ui/button";
import {  Plus, Trash2 } from "lucide-react";
import { useState } from "react";

interface IntroductoryVideoProps {
  videoUrl?: string;
}

function IntroductoryVideo({ videoUrl }: IntroductoryVideoProps) {
  const [isEditing, setIsEditing] = useState(false);
  console.log(isEditing)

  return (
    <div className="mb-8">
      <h2 className="text-base mb-4">Introductory video</h2>
      
      {videoUrl ? (
        <div className="relative rounded-lg overflow-hidden aspect-video max-w-2xl">
          <video 
            src={videoUrl} 
            controls 
            className="w-full h-full object-cover"
          />
          <Button 
            variant="outline"
            size="sm"
            className="absolute top-4 rounded-full right-4 bg-white"
            onClick={() => setIsEditing(true)}
          >
            <Trash2  />
          </Button>
        </div>
      ) : (
        <div className="flex items-center justify-center border-2 border-dashed border-gray-300 rounded-lg p-8 max-w-2xl aspect-video">
          <Button 
            variant="outline"
            onClick={() => setIsEditing(true)}
            className="flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Add video
          </Button>
        </div>
      )}
    </div>
  );
}

export default IntroductoryVideo;
