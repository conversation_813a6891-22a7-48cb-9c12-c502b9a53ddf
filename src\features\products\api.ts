import api from "@/lib/axios";
import { CartApiPayload, ProductQueryParams } from "./type";

export const getProductDetailsApi = async (productId: string) => {
  const { data } = await api.get(`/api/v1/product/${productId}`);
  return data;
};

export const fetchProductsApi = async (params: ProductQueryParams) => {
  let updatedParams = {};

  if (params.category) {
    updatedParams = { ...updatedParams, category: params.category };
  }
  if (params.brand) {
    updatedParams = { ...updatedParams, brand: params.brand };
  }
  if (params.minPrice) {
    updatedParams = { ...updatedParams, minPrice: params.minPrice };
  }
  if (params.maxPrice) {
    updatedParams = { ...updatedParams, maxPrice: params.maxPrice };
  }
  if (params.page) {
    updatedParams = { ...updatedParams, page: params.page };
  }
  const { data } = await api.get("/api/v1/product", { params: updatedParams });
  return data;
};

export const fetchCategoriesApi = async () => {
  const { data } = await api.get("/api/v1/product/categories");
  return data;
};
export const fetchBrandsApi = async () => {
  const { data } = await api.get("/api/v1/product/brands");
  return data;
};

export const addToCartApi = async (payload: CartApiPayload) => {
  const { data } = await api.post("/api/v1/cart", payload);
  return data;
};

export const getSellerDetailsApi = async (sellerId: string) => {
  const { data } = await api.get(`/api/v1/users/seller/${sellerId}`);
  return data;
};
export const getSellerProductsDetailsApi = async (sellerId: string) => {
  const { data } = await api.get(`/api/v1/users/seller/${sellerId}/products`);
  return data;
};

export const getProductReviewsApi = async (productId: string, page: number) => {
  const { data } = await api.get(`/api/v1/review/product/${productId}`, {
    params: { page },
  });
  return data.data;
};
