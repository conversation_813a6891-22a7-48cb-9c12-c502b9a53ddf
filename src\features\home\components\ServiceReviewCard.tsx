import { ServiceProviderProps } from "../types";
import { IoLocationOutline } from "react-icons/io5";
import RatingStar from "@/assets/rating-star.png";
import CalendarIcon from "@/assets/calendar.png";

function ServiceCard({
  image,
  name,
  specialty,
  rating,
  experience,
  services,
}: Omit<ServiceProviderProps, "id">) {
  return (
    <div className="flex min-h-[190px] gap-y-5 flex-col h-full">
      <div className="flex items-start gap-4 ">
        <div className="w-[72px] h-[72px] rounded-full overflow-hidden flex-shrink-0">
          <img src={image} alt={name} className="w-full h-full object-cover" />
        </div>
        <div className="flex-1">
          <div className="flex items-start justify-between">
            <div>
              <h3 className="text-lg font-medium">{name}</h3>
              <p className="text-sm text-gray-500 mt-1">{specialty}</p>
            </div>
            <div className="flex items-center my-auto">
              <img src={RatingStar} className="text-orange-1 mr-1 h-5 text-lg" />
              <span className="font-medium">{rating}</span>
            </div>
          </div>
        </div>
      </div>
      <div className="flex justify-between w-9/12 my-auto gap-6 ">
        <div className="flex items-center gap-1">
          <IoLocationOutline className="text-gray-500 " />
          <span className="text-sm text-gray-500">Virtual</span>
        </div>
        <div className="flex items-center gap-1">
          <img src={CalendarIcon} className="text-gray-500 text-sm" />
          <span className="text-sm text-gray-500">{experience}</span>
        </div>
      </div>
      <div className="w-10/12">
        <p className="text-lg	 text-neutral-900">{services.join(", ")}</p>
      </div>
    </div>
  );
}

export default ServiceCard;
