import { Modal } from "@/components/ui/modal";
import { fetchServiceDetailsApi } from "../api";
import { useQuery } from "@tanstack/react-query";
import CloseIcon from "@/assets/close-icon.svg";
import GoBackIcon from "@/assets/go-back.svg";
import TickIcon from "@/assets/tick.svg";
import ProductDetailSkelton from "./ProductDetailSkelton";

function ServiceDetailsModal({
  isOpen,
  onClose,
  selectedServiceId,
  openProviderServices,
}: {
  isOpen: boolean;
  onClose: () => void;
  selectedServiceId: string;
  openProviderServices: () => void;
}) {
  const {
    data: service,
    isPending: loading,
    isSuccess,
  } = useQuery({
    queryKey: ["service-details", selectedServiceId],
    queryFn: () => fetchServiceDetailsApi(selectedServiceId),
    enabled: !!selectedServiceId && isOpen,
  });

  const handleGoBack = () => {
    openProviderServices();
    onClose();
  };

  return (
  
    <Modal className="lg:min-w-[700px]" isOpen={isOpen} onClose={onClose}>
      <div>
        <div className="flex items-center justify-between mb-6">
          <div className="flex cursor-pointer gap-x-4" onClick={handleGoBack}>
            <img
              className="transition-opacity hover:opacity-70"
              src={GoBackIcon}
              alt="go back"
            />
            <h1 className="text-gray-900 ">Go back</h1>
          </div>
          <img
            onClick={onClose}
            className="transition-opacity cursor-pointer hover:opacity-70"
            src={CloseIcon}
            alt="close"
          />
        </div>
        {loading ? (
          <ProductDetailSkelton />
        ) : (
          isSuccess && (
            <div className="mt-8">
              <div className="flex flex-col justify-between p-3 border rounded-lg gap-y-5 border-tints-50">
                <div className="flex justify-between">
                  <div className="flex gap-x-2">
                    <div>
                      <h1 className="text-lg font-semibold">{service.title}</h1>
                      <p className=" text-neutral-300">
                        {service.duration > 1
                          ? `${service.duration} hours`
                          : `${service.duration} hour`}
                      </p>
                    </div>
                  </div>
                  <div className="my-auto">
                    <p className="my-auto text-lg font-semibold">
                      ${service?.price}
                    </p>
                  </div>
                </div>
                 {/* service highlights */}
                 <div className="p-3 rounded-md bg-tints-50">
                  <h1 className="text-xs text-neutral-600 ">SERVICE HIGHLIGHTS</h1>
                  <div className="grid grid-cols-12 mt-4 gap-y-3">
                    {service?.highlights?.map((highlight, index) => (
                      <div key={index} className="flex col-span-1 md:col-span-3 lg:col-span-4 gap-x-2">
                        <img src={TickIcon} alt="tick " />
                        <p className="text-sm text-neutral-600">{highlight}</p>
                      </div>
                    ))}
                  </div>
                 </div>
                <div className="overflow-y-scroll max-h-[250px] gap-x-2 scrollbar-hide">
                  <p className="mt-2 text-neutral-600 ">
                    {service?.description}
                  </p>
                </div>
              </div>
            </div>
          )
        )}
      </div>
    </Modal>
  );
}

export default ServiceDetailsModal;
