import React from "react";
import { createPortal } from "react-dom";

type ModalPosition =
  | "center"
  | "top-right"
  | "top-center"
  | "bottom-left"
  | "bottom-right";

interface CustomModalProps {
  isOpen: boolean;
  onClose: () => void;
  position?: ModalPosition;
  children: React.ReactNode;
  className?: string;
  restrictClose?: boolean;
}

export const Modal: React.FC<CustomModalProps> = ({
  isOpen,
  onClose,
  position = "center",
  children,
  className,
  restrictClose = false,
}) => {
  if (!isOpen) return null;

  const positionClasses: Record<ModalPosition, string> = {
    center: "top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2",
    "top-right": "top-10 right-10",
    "bottom-left": "bottom-10 left-10",
    "bottom-right": "bottom-10 right-10",
    "top-center": "top-10 left-1/2 -translate-x-1/2",
  };

  return createPortal(
    <div
      role="dialog"
      aria-modal="true"
      className="fixed inset-0 z-50 flex "
      onClick={!restrictClose ? onClose : undefined}
    >
      {/* Overlay */}
      <div className="w-[100vw] h-[100vh] flex content-center transition-opacity bg-black/20 backdrop-blur-sm" />

      {/* Modal content */}
      <div
        onClick={(e) => e.stopPropagation()}
        className={`fixed ${className} ${positionClasses[position]}  max-w-10/12 mx-auto md:min-w-[480px] rounded-lg flex flex-col gap-y-2 z-60 bg-white p-6  w-11/12 md:w-auto shadow-lg transition-all `}
      >
        {children}
      </div>
    </div>,
    document.body
  );
};
