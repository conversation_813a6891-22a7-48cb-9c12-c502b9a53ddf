import { useNavigate } from "react-router-dom";
import { ServiceDetails } from "./ServiceDetailsDialog";

type ServiceCardProps = {
  service: ServiceDetails & { id?: string };
};

function ServiceCard({ service }: ServiceCardProps) {
  const navigate = useNavigate();

  const handleCardClick = () => {
    // Use service id if available, otherwise use service name as fallback
    const serviceId = service.id || service.serviceName.toLowerCase().replace(/\s+/g, '-');
    navigate(`/provider/services/${serviceId}`);
  };

  return (
    <div
      onClick={handleCardClick}
      className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow cursor-pointer"
    >
      <div className="flex justify-between items-start mb-2">
        <div>
          <h2 className="text-lg font-semibold">{service.serviceName}</h2>
          <p className="text-gray-600">{service.duration}</p>
        </div>
        <div>
          <span className="font-semibold text-lg">${service.price}</span>
        </div>
      </div>
      <p className="text-gray-700 line-clamp-4">{service.description}</p>
    </div>
  );
}

export default ServiceCard;
