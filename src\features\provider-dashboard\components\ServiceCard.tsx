import { ServiceDetails } from "./ServiceDetailsDialog";

type ServiceCardProps = {
  service: ServiceDetails;
};

function ServiceCard({ service }: ServiceCardProps) {
  return (
    <div className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow cursor-pointer">
      <div className="flex justify-between items-start mb-2">
        <div>
          <h2 className="text-lg font-semibold">{service.serviceName}</h2>
          <p className="text-gray-600">{service.duration}</p>
        </div>
        <div>
          <span className="font-semibold text-lg">${service.price}</span>
        </div>
      </div>
      <p className="text-gray-700 line-clamp-4">{service.description}</p>
    </div>
  );
}

export default ServiceCard;
