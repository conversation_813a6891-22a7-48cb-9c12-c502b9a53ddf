import NurtureLogo from "@/assets/nurture-logo.png";
import SellersLogo from "@/assets/sellers.png";
import Provider<PERSON>ogo from "@/assets/provider.png";
import MessageLogo from "@/assets/message.png";
import LogoutLogo from "@/assets/logout.png";
import { Link, useLocation } from "react-router-dom";
import LogoutModal from "@/components/ui/logout-modal";
import { useState } from "react";
function Navbar() {
  const [isOpen, setIsOpen] = useState(false);
  const onClose = () => {
    setIsOpen(false);
  };
  const onOpen = () => {
    setIsOpen(true);
  };
  const navbarItems = [
    { icon: SellersLogo, name: "Sellers", href: "/admin/sellers" },
    { icon: ProviderLogo, name: "Providers", href: "/admin/providers" },
    { icon: MessageLogo, name: "Messages", href: "/admin/messages" },
  ];
  const { pathname } = useLocation();
  const key = pathname.split("/")[2];

  return (
    <>
    <aside className="flex flex-col justify-between h-full p-5 gap-y-5">
      <div className="max-h-[120px] max-w-[170px]">
        <img src={NurtureLogo} alt="nurture " />
      </div>
      <div className="mt-5 grow">
        <ul>
          {navbarItems.map((item) => (
            <Link
              to={item.href}
              key={item.name}
              className="my-5 cursor-pointer "
            >
              <li
                className={`${
                  key === item.name.toLocaleLowerCase() ? "bg-tints-200" : ""
                } flex gap-x-3 my-5`}
                key={item.name}
              >
                <div
                  className={`w-1 ${
                    key === item.name.toLocaleLowerCase()
                      ? "bg-orange-1"
                      : "bg-transparent"
                  }`}
                ></div>

                <div className="flex py-2 my-auto gap-x-3">
                  <img className="my-auto" src={item.icon} alt={item.name} />
                  <span
                    className={`w-1 ${
                      key === item.name.toLocaleLowerCase()
                        ? "font-semibold"
                        : "font-normal"
                    }`}
                  >
                    {item.name}
                  </span>
                </div>
              </li>
            </Link>
          ))}
        </ul>
      </div>
      <div>
        <div
          className="flex ml-5 cursor-pointer gap-x-3"
          onClick={onOpen}
        >
          <img className="my-auto" src={LogoutLogo} alt="logout" />
          <p>logout</p>
        </div>
      </div>
    </aside>
    <LogoutModal isOpen={isOpen} onClose={onClose} />
    </>
  );
}

export default Navbar;
