import { BrowserRouter, Route, Routes } from "react-router-dom";
import User from "./routes/UserRoute";
import Seller from "./routes/SellerRoute";
import Admin from "./routes/AdminRoute";
import Provider from "./routes/ProviderRoute";
import { Toaster } from "react-hot-toast";
import ScrollToTop from "./components/ui/scroll-to-top";

function App() {
  return (
    <>
      <BrowserRouter>
        <ScrollToTop />
        <Routes>
          <Route path="/*" element={<User />}></Route>
          <Route path="/seller/*" element={<Seller />}></Route>
          <Route path="/admin/*" element={<Admin />}></Route>
          <Route path="/provider/*" element={<Provider />}></Route>
        </Routes>
      </BrowserRouter>
      <Toaster
        position="top-center"
        reverseOrder={false}
        toastOptions={{
          style: {
            background: "#ffff",
            color: "#665F5c",
          },
        }}
      />
    </>
  );
}

export default App;
