import { But<PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useState } from "react";

interface Photo {
  id: string;
  url: string;
}

interface PhotosProps {
  photos: Photo[];
}

function Photos({ photos = [] }: PhotosProps) {
  const [isAdding, setIsAdding] = useState(false);

  return (
    <div className="mb-8">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-base">Photos</h2>
        <Button
          variant="outline"
          onClick={() => setIsAdding(true)}
          className="flex items-center gap-2 text-orange-1 border-orange-1"
        >
          Add <Plus className="w-4 h-4" />
        </Button>
      </div>

      {photos.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
          {photos.map((photo) => (
            <div
              key={photo.id}
              className="relative aspect-square rounded-lg overflow-hidden group"
            >
              <img
                src={photo.url}
                alt="Provider photo"
                className="w-full h-full object-cover"
              />
            </div>
          ))}
        </div>
      ) : (
        <div className="flex items-center justify-center border-2 border-dashed border-gray-300 rounded-lg p-8 max-w-2xl aspect-video">
          <p className="text-gray-500">No photos added yet.</p>
        </div>
      )}

      {isAdding && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg max-w-md w-full">
            <h3 className="text-lg font-semibold mb-4">Add Photos</h3>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 flex flex-col items-center justify-center">
              <input
                type="file"
                accept="image/*"
                className="hidden"
                id="photo-upload"
                multiple
              />
              <label
                htmlFor="photo-upload"
                className="cursor-pointer flex flex-col items-center"
              >
                <Plus className="w-8 h-8 text-gray-400 mb-2" />
                <span className="text-gray-500">Click to upload</span>
              </label>
            </div>
            <div className="flex justify-end gap-2 mt-4">
              <Button
                variant="outline"
                onClick={() => setIsAdding(false)}
              >
                Cancel
              </Button>
              <Button>
                Upload
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default Photos;
