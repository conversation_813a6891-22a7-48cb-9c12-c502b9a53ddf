
function DetailRow({
  label,
  value,
}: {
  label: string;
  value: string | number;
}) {
  return (
    <div className="grid grid-cols-12 py-4 border-t border-gray-200 first:border-t-0 hover:bg-gray-50">
      <span className="col-span-5 text-sm text-neutral-300 text-start">
        {label}
      </span>
      <span className="col-span-2 text-center text-neutral-600">:</span>
      <span className="col-span-5 text-sm font-medium text-gray-900 ">
        {value}
      </span>
    </div>
  );
}

export default DetailRow;
