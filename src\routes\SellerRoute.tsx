import { Route, Routes } from "react-router-dom";
import ProtectedRoutes from "./ProtectedRoutes";
import SellerRegister from "@/features/auth-seller/pages/Register";
import SellerLogin from "@/features/auth-seller/pages/Login";
import SellerOtp from "@/features/auth-seller/pages/Otp";
import SellerProfileSetup from "@/features/auth-seller/pages/ProfileSetup";
import SellerAddProduct from "@/features/auth-seller/pages/AddProduct";
import VerifyAccount from "@/features/auth-seller/pages/VerifyAccount";
import ForgotPassword from "@/features/auth-seller/pages/ForgotPassword";
import CheckEmail from "@/features/auth-seller/pages/CheckEmail";
import SellerResetPassoword from "@/features/auth-seller/pages/ResetPassoword";
import SellerDashboard from "@/features/seller-dashboard/pages/SellerDashboard";
import SellerOrders from "@/features/seller-orders/pages/SellerOrders";
import NotFound from "@/components/ui/not-found";
import SellerProductDetails from "@/features/seller-dashboard/pages/SellerProductDetails";
import OrderProductDetail from "@/features/seller-orders/pages/OrderProductDetail";
import SellerBasicDetail from "@/features/seller-account/pages/BasicDetail";
import SellerAccount from "@/features/seller-account/pages/Account";
import SellerPromotionalOffers from "@/features/seller-account/pages/PromotionalOffers";
import SellerReview from "@/features/seller-review/pages/SellerReview";
import SellerChatPage from "@/features/chat/pages/SellerChatPage";
function SellerRoute() {
  return (
    <Routes>
      <Route element={<ProtectedRoutes role="seller" />}>
        <Route path={"/orders"} element={<SellerOrders />} />
        <Route path={"/reviews"} element={<SellerReview />} />
        <Route path={"/orders/:id"} element={<OrderProductDetail />} />
        <Route path={"/dashboard"} element={<SellerDashboard />} />
        <Route path={"/products/:id"} element={<SellerProductDetails />} />
        <Route path={"/account"} element={<SellerAccount />} />
        <Route
          path={"/account/basic-details"}
          element={<SellerBasicDetail />}
        />
        <Route
          path={"/account/promotional-offers"}
          element={<SellerPromotionalOffers />}
        />
      </Route>
      <Route path={"/chat"} element={<SellerChatPage />} />
      <Route path={"/register"} element={<SellerRegister />} />
      <Route path={"/login"} element={<SellerLogin />} />
      <Route path={"/verify-otp"} element={<SellerOtp />} />
      <Route path={"/setup-profile"} element={<SellerProfileSetup />} />
      <Route path={"/setup-profile/product"} element={<SellerAddProduct />} />
      <Route path={"/verify-account"} element={<VerifyAccount />} />
      <Route path={"/forgot-password"} element={<ForgotPassword />} />
      <Route path={"/forgot-password/check-email"} element={<CheckEmail />} />
      <Route path={"/reset-password"} element={<SellerResetPassoword />} />
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
}

export default SellerRoute;
