import ServiceBanner from "@/assets/service-banner.png";
import ServiceBannerMobile from "@/assets/service-banner-mobile.png";
import Buttonwithicon from "@/components/ui/buttonwithicon";
import { ServiceProviderProps } from "../types";
import ServiceReviewCard from "./ServiceReviewCard";

function ServiceSection() {
  // Sample service provider data - replace with actual data from API or CMS
  const serviceProviders: ServiceProviderProps[] = [
    {
      id: 1,
      image: ServiceBanner, // Replace with actual provider image
      name: "<PERSON>",
      specialty: "Specialty in new-born Feeding",
      rating: 4.6,
      experience: "3 yrs exp",
      services: [
        "Lactation consulting",
        "Doula Services",
        "Infant Care Nursing",
      ],
    },
    {
      id: 2,
      image: ServiceBanner, // Replace with actual provider image
      name: "<PERSON>",
      specialty: "Sleep Support",
      rating: 4.6,
      experience: "3 yrs exp",
      services: [
        "Lactation consulting",
        "Doula Services",
        "Infant Care Nursing",
      ],
    },
    {
      id: 3,
      image: ServiceBanner, // Replace with actual provider image
      name: "<PERSON>",
      specialty: "Physical Recovery",
      rating: 4.6,
      experience: "3 yrs exp",
      services: [
        "Lactation consulting",
        "Doula Services",
        "Infant Care Nursing",
      ],
    },
    {
      id: 4,
      image: ServiceBanner, // Replace with actual provider image
      name: "Ashley Brown",
      specialty: "Specialty in new-born Care",
      rating: 4.6,
      experience: "3 yrs exp",
      services: [
        "Lactation consulting",
        "Doula Services",
        "Infant Care Nursing",
      ],
    },
  ];

  return (
    <section className="bg-tints-40 rounded-lg">
      <div className="flex flex-col md:flex-row gap-x-3">
        <div className="h-60 md:h-auto md:w-[45%] ">
          <img src={ServiceBanner} alt="service" className="w-full hidden md:block h-full object-cover"/>
          <img src={ServiceBannerMobile} alt="service" className="w-full  md:hidden h-full object-cover"/>
        </div>
        {/* Header section */}
        <div className="p-4 md:p-8 lg:p-16 md:w-2/3 lg:w-3/4">
          <div className="mb-6 md:mb-8">
            <div className="uppercase text-orange-1 text-xs md:text-base font-prettywise font-medium tracking-wider mb-2">
              RECOMMENDED SERVICES
            </div>

            <div className="flex flex-col md:gap-y-5">
              <h2 className="text-xl md:text-2xl lg:text-4xl font-medium max-w-xl mb-4 md:mb-0">
                We know your needs during <br/> different post partum stages
              </h2>

              <div className="self-start md:self-auto">
                <Buttonwithicon variant="default" text="
                See all" href="/services" />
              </div>
            </div>
          </div>

          {/* Service Providers - stacked on mobile, grid on larger screens */}
          <div className="pb-4">
            <div className="grid  grid-cols-1 gap-4 md:grid-cols-2">
              {serviceProviders.map((provider) => (
                <div
                  key={provider.id}
                  className="border h-full p-5 rounded-lg border-neutral-40 bg-white shadow-sm"
                >
                  <ServiceReviewCard
                    image={provider.image}
                    name={provider.name}
                    specialty={provider.specialty}
                    rating={provider.rating}
                    experience={provider.experience}
                    services={provider.services}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

export default ServiceSection;
