import { useState, useRef } from "react";
import FingerPrintImage from "@/assets/fingerprint.png";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Link, useNavigate } from "react-router-dom";
import { useLocation } from "react-router-dom";
import { verifyOtpApi } from "../api";
import { useMutation } from "@tanstack/react-query";
import { showToast } from "@/lib/toast";
import { AxiosError } from "axios";
function OtpForm() {
  const location = useLocation();
  const navigate = useNavigate();
  const state = location.state as { email: string };
  const [otp, setOtp] = useState(["", "", "", "", "", ""]);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  const handleChange = (value: string, index: number) => {
    if (value.length > 1) return;
    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    // Move focus to the next input
    if (value && index < otp.length - 1) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyDown = (
    e: React.KeyboardEvent<HTMLInputElement>,
    index: number
  ) => {
    if (e.key === "Backspace" && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const { mutate, isPending } = useMutation({
    mutationFn: verifyOtpApi,
    onSuccess: () => {
      navigate("/seller/setup-profile");
    },
    onError: (error: AxiosError) => {
      if (error.response?.status === 401) {
        showToast("Invalid OTP", "error");
        return;
      }
      showToast("Something went wrong", "error");
    },
  });
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const otpValue = otp.join("");
    if (otpValue.length !== 6) return;
    mutate({ otp: otpValue, email: state?.email });
  };
  return (
    <div className="flex flex-col items-center justify-center mx-auto md:mx-0 w-full max-w-md text-center space-y-6">
      <img
        src={FingerPrintImage}
        alt="Fingerprint"
        className="mx-auto object-cover"
      />
      <h1 className="text-3xl font-prettywise font-bold">Verify Email</h1>
      <p className="text-gray-500">
        Please enter the 6 digit OTP that we've sent to your email
        <br />
        <span className="font-semibold">{state?.email}</span>
      </p>
      <form className="flex flex-col gap-y-8" onSubmit={handleSubmit}>
        <div className="flex justify-center gap-4">
          {otp.map((digit, index) => (
            <Input
              key={index}
              ref={(el) => {
                inputRefs.current[index] = el;
              }}
              type="numvber"
              pattern="\d"
              maxLength={1}
              inputMode="numeric"
              value={digit}
              onChange={(e) => handleChange(e.target.value, index)}
              onKeyDown={(e) => handleKeyDown(e, index)}
              className="md:w-12 md:h-12 shrink text-center text-xl border border-input-border focus:border-orange-1"
            />
          ))}
        </div>
        <Button disabled={isPending} className="w-full">
          Verify email
        </Button>
      </form>
      <Link
        to="/seller/register"
        className="flex items-center gap-2 font-semibold"
      >
        <span>&larr;</span> Go back
      </Link>
    </div>
  );
}

export default OtpForm;
