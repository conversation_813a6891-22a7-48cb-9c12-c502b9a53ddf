import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useForm } from "react-hook-form";
import { joiResolver } from "@hookform/resolvers/joi";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { basicDetailsSchema } from "../validation";
import { fetchUserProfileApi, updateUserProfileApi } from "../api";
import { BasicDetailsData } from "../type";
import { showToast } from "@/lib/toast";
import { useEffect } from "react";

function BasicDetailsForm() {
  const queryClient = useQueryClient();

  const {
    register,
    handleSubmit,
    setValue,
    reset,
    watch,
    formState: { errors },
  } = useForm<BasicDetailsData>({
    resolver: joiResolver(basicDetailsSchema),
  });

  // Watch the form values to get real-time updates
  const birthStatusValue = watch("birthStatus");
  const preferredLanguageValue = watch("preferredLanguage");

  const { data, isLoading } = useQuery({
    queryKey: ["user-profile"],
    queryFn: fetchUserProfileApi,
    retry: 2,
    staleTime: 30000, // 30 seconds
  });

  useEffect(() => {
    // Check for loggedInCustomer in the response (the actual structure from the API)
    if (data?.data?.loggedInCustomer) {
      const { firstName, lastName, phone, birthStatus, preferredLanguage } =
        data.data.loggedInCustomer;
      // Reset the form with the user data
      reset({
        firstName,
        lastName,
        phone,
        birthStatus,
        preferredLanguage,
      });

      // Also set the values individually to ensure they're properly updated
      setValue("firstName", firstName);
      setValue("lastName", lastName);
      setValue("phone", phone);
      setValue("birthStatus", birthStatus);
      setValue("preferredLanguage", preferredLanguage);
    } else {
      console.log("No user data found in response");
    }
  }, [data, reset, setValue]);

  const { mutate, isPending } = useMutation({
    mutationFn: updateUserProfileApi,
    onSuccess: () => {
      showToast("Profile updated successfully", "success");
      queryClient.invalidateQueries({ queryKey: ["user-profile"] });
    },
    onError: (error) => {
      console.error("Error updating profile:", error);
      showToast("Something went wrong", "error");
    },
  });

  const onSubmit = (formData: BasicDetailsData) => {
    console.log("Submitting form data:", formData);
    mutate(formData);
  };

  if (isLoading) {
    return (
      <div className="w-full space-y-4">
        <div className="w-full space-y-4 animate-pulse">
          <div className="h-10 bg-gray-200 rounded"></div>
          <div className="h-10 bg-gray-200 rounded"></div>
          <div className="h-10 bg-gray-200 rounded"></div>
          <div className="h-10 bg-gray-200 rounded"></div>
          <div className="h-10 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col w-full space-y-5">
      <form className="w-full space-y-4" onSubmit={handleSubmit(onSubmit)}>
        <div className="flex gap-4">
          <div className="w-full">
            <Input
              type="text"
              placeholder="First name"
              {...register("firstName")}
              className="w-full border border-input-border"
            />
            {errors.firstName && (
              <p className="text-sm text-red-500">{errors.firstName.message}</p>
            )}
          </div>
          <div className="w-full">
            <Input
              type="text"
              placeholder="Last name"
              {...register("lastName")}
              className="w-full border border-input-border"
            />
            {errors.lastName && (
              <p className="text-sm text-red-500">{errors.lastName.message}</p>
            )}
          </div>
        </div>
        <div>
          <Input
            type="number"
            inputMode="numeric"
            placeholder="Phone number"
            {...register("phone")}
            className="w-full border border-input-border"
          />
          {errors.phone && (
            <p className="text-sm text-red-500 text-start">
              {errors.phone.message}
            </p>
          )}
        </div>
        <div>
          <Select
            onValueChange={(
              value: "currently pregnant" | "loss history" | "postpartum"
            ) => {
              setValue("birthStatus", value);
              console.log("Birth status changed to:", value);
            }}
            value={
              birthStatusValue ||
              data?.data?.loggedInCustomer?.birthStatus ||
              ""
            }
          >
            <SelectTrigger className="w-full border-input-border">
              <SelectValue placeholder="Birth Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="currently pregnant">
                Currently pregnant
              </SelectItem>
              <SelectItem value="postpartum">Postpartum</SelectItem>
              <SelectItem value="loss history">Loss history</SelectItem>
            </SelectContent>
          </Select>
          {errors.birthStatus && (
            <p className="text-sm text-red-500 text-start">
              {errors.birthStatus.message}
            </p>
          )}
        </div>
        <div>
          <Select
            onValueChange={(value) => {
              setValue("preferredLanguage", value);
              console.log("Preferred language changed to:", value);
            }}
            value={
              preferredLanguageValue ||
              data?.data?.loggedInCustomer?.preferredLanguage ||
              ""
            }
          >
            <SelectTrigger className="w-full border-input-border">
              <SelectValue placeholder="Preferred Language" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="english">English</SelectItem>
              {/* Add more language options here if needed */}
            </SelectContent>
          </Select>
          {errors.preferredLanguage && (
            <p className="text-sm text-red-500 text-start">
              {errors.preferredLanguage.message}
            </p>
          )}
        </div>
        <div>
          <Button type="submit" className="w-full mt-6" disabled={isPending}>
            {isPending ? "Updating..." : "Update Profile"}
          </Button>
        </div>
      </form>
    </div>
  );
}

export default BasicDetailsForm;
