import { useState } from "react";
import Layout from "../components/Layout";
import ProviderTable from "../components/ProviderTable";
import { showToast } from "@/lib/toast";
import { useMutation, useQuery } from "@tanstack/react-query";
import { ApproveProviderApi, fetchProvidersApi } from "../api";

function Providers() {
  const [currentPage, setCurrentPage] = useState(1);
  const [focus, setFocus] = useState("all");
  let totalPages = 1;

  const {
    data,
    isPending: loading,
    isSuccess,
    refetch,
  } = useQuery({
    queryKey: ["providers", currentPage, focus],
    queryFn: () => fetchProvidersApi({ type: focus, page: currentPage }),
  });
  if (isSuccess) {
    totalPages = data?.data?.totalPages;
  }
  const { mutate } = useMutation({
    mutationFn: ApproveProviderApi,
    onSuccess: ({ data }) => {
      const toastMessage = `Provider ${
        data.provider.isApproved ? "approved" : "rejected"
      } successfully!`;
      refetch();
      showToast(toastMessage, "success");
    },
    onError: () => {
      showToast("failed to approve provider", "error");
    },
  });

  const handleProviderApprove = (providerId: string, isApproved: boolean) => {
    mutate({ providerId, isApproved });
  };
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  return (
    <Layout>
      <div className="p-6 bg-white rounded-lg">
        <div className="mb-6">
          <h1 className="mb-2 text-2xl font-bold">Providers</h1>
          <div className="flex space-x-4">
            <button
              onClick={() => setFocus("all")}
              className={`${
                focus === "all" ? "border-b-4 border-orange-1 font-bold" : ""
              }  font-medium pb-2`}
            >
              All
            </button>
            <button
              onClick={() => setFocus("pending")}
              className={`${
                focus === "pending"
                  ? "border-b-4 border-orange-1 font-bold"
                  : ""
              }  font-medium pb-2`}
            >
              Pending verification
            </button>
          </div>
        </div>

        <ProviderTable
          data={data?.data?.providers}
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          focus={focus}
          loading={loading}
          handleProviderApprove={handleProviderApprove}
        />
      </div>
    </Layout>
  );
}

export default Providers;
