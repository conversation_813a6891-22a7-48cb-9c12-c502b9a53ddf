import { Button } from "@/components/ui/button";
import { Modal } from "@/components/ui/modal";
import { useAuthStore } from "@/store/authStore";

function LogoutModal({
  isOpen,
  onClose,
}: {
  isOpen: boolean;
  onClose: () => void;
}) {
  const { logout } = useAuthStore((state) => state);
  const role = localStorage.getItem("nurtureUser");
  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <div className="">
        <h2 className="mb-2 text-lg font-semibold text-gray-900">
          Are you sure you want to logout?
        </h2>
        <div className="flex justify-end gap-4 mt-5">
          <Button variant={"outline"} onClick={onClose}>
            No
          </Button>
          <Button
            className="rounded-full"
            variant={"destructive"}
            onClick={() => logout(role as string)}
          >
            Yes, Logout
          </Button>
        </div>
      </div>
    </Modal>
  );
}

export default LogoutModal;
