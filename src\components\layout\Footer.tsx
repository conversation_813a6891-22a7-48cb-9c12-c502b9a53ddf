import { Link } from "react-router-dom";
import FacebookIcon from "@/assets/fb-icon.png";
import InstagramIcon from "@/assets/instagram-icon.png";
import TiktokIcon from "@/assets/tik-tok.png";
function Footer() {
  return (
    <footer className="py-8 mt-10 bg-white border-t border-gray-200 md:mt-20">
      <div className="container px-4 mx-auto md:px-8">
        <div className="flex flex-col items-start justify-between space-y-8 md:flex-row md:items-center md:space-y-0">
          {/* Left Section */}
          <div className="w-full space-y-4 md:w-1/2">
            <div>
              <h2 className="w-1/4 text-2xl text-center font-prettywise">nurture</h2>
              <h2 className="text-2xl font-prettywise">postnatal care</h2>
            </div>
            <p className="text-sm text-gray-500 text-balance ">
              Lorem ipsum dolor sit amet consectetur. Sit ultrices eget libero
              nulla tortor aenean curabitur id. Nibh elementum sollicitudin
              commodo nunc.
            </p>
            <div className="flex space-x-4 text-orange-500">
              <img
                src={FacebookIcon}
                className="w-5 h-5 font-light cursor-pointer"
              />
              <img
                src={InstagramIcon}
                className="w-5 h-5 font-light cursor-pointer"
              />
              <img
                src={TiktokIcon}
                className="w-5 h-5 font-light cursor-pointer"
              />
            </div>
          </div>

          {/* Middle Section */}
          <div className="flex flex-row justify-between w-full md:justify-evenly md:w-3/4">
            <div className="space-y-2 ">
              <Link to={"/shop"} className="block">
                Shop
              </Link>
              <Link to={"/services"} className="block">
                Services
              </Link>
              <Link to={"/"} className="block">
                Resources
              </Link>
            </div>
            <div className="space-y-2">
              <Link to={"/"} className="block">
                Privacy policy
              </Link>
              <Link to={"/"} className="block">
                Terms & Conditions
              </Link>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="pt-4 mt-8 text-sm text-center text-gray-500 border-t border-gray-200">
          © 2025 Nurture, Inc
        </div>
      </div>
    </footer>
  );
}

export default Footer;
