import api from "@/lib/axios";
import { checkAuthentication } from "@/lib/utils";
import { useAuthStore } from "@/store/authStore";
import { AxiosError } from "axios";
import { useEffect, useState } from "react";
import { Outlet, useNavigate } from "react-router-dom";
type ProtectedProps = {
  role: "admin" | "seller" | "provider" | "customer";
};
function ProtectedRoutes({ role }: ProtectedProps) {
  const [auth, setAuth] = useState<boolean | null>(null);
  const [isAllowed, setIsAllowed] = useState<boolean | null>(null);
  const { login } = useAuthStore((state) => state);
  const { href, role: currentRole } = checkAuthentication();
  const navigate = useNavigate();
  useEffect(() => {
    if (!currentRole) {
      navigate(-1);
      return;
    }
    api
      .post("/api/v1/auth/refresh-token", { role })
      .then(async ({ data }) => {
        if (data.data.accessToken) {
          setAuth(true);
          login({
            accessToken: data.data.accessToken,
            role: currentRole,
          });
          setIsAllowed(data.data.isAllowed);
        }
      })
      .catch(async (error: AxiosError) => {
        console.error("Error refreshing token:", error);
        setAuth(false);
        setIsAllowed(false);
      });
  }, []);

  if (auth === null || isAllowed === null) return null;

  if (!auth) {
    navigate(-1);
    return null;
  }
  if (!isAllowed) {
    navigate(href);
    return null;
  }
  return <Outlet />;
}

export default ProtectedRoutes;
