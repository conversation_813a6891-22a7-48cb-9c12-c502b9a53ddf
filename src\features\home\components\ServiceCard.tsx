import { ServiceProviderProps } from "../type";
import { IoLocationOutline } from "react-icons/io5";
import RatingStar from "@/assets/rating-star.png";
import CalendarIcon from "@/assets/calendar.png";
import { Link } from "react-router-dom";

function ServiceCard({
  image,
  name,
  specialty,
  rating,
  experience,
  services,
  id,
}: ServiceProviderProps) {
  return (
    <div className="flex min-h-[190px] gap-y-5 flex-col h-full">
      <div className="flex items-start gap-4 ">
        <div className="w-[72px] h-[72px] rounded-full overflow-hidden flex-shrink-0">
          <Link to={`/provider/${id}`}>
            <img
              src={image}
              alt={name}
              className="object-cover w-full h-full"
            />
          </Link>
        </div>
        <div className="flex-1">
          <div className="flex items-start justify-between">
            <div>
              <h3 className="text-lg font-medium">{name}</h3>
              <p className="mt-1 text-sm text-neutral-300">{specialty}</p>
            </div>
            <div className="flex items-center my-auto">
              <img
                src={RatingStar}
                className="h-5 mr-1 text-lg text-orange-1"
              />
              <span className="font-medium">{rating}</span>
            </div>
          </div>
        </div>
      </div>
      <div className="flex justify-between w-9/12 gap-6 my-auto ">
        <div className="flex items-center gap-1">
          <IoLocationOutline className="text-neutral-300 " />
          <span className="text-sm text-neutral-300">Virtual</span>
        </div>
        <div className="flex items-center gap-1">
          <img src={CalendarIcon} className="text-sm text-neutral-300" />
          <span className="text-sm text-neutral-300">{experience} yrs exp</span>
        </div>
      </div>
      <div className="w-full">
        {services.services.map((service) => (
          <>
            <Link
              key={service.id}
              className="mx-1 first:mx-0 hover:underline"
              to={`/services/${service.id}`}
            >
              {service.title}
            </Link>
            ,
          </>
        ))}
      </div>
    </div>
  );
}

export default ServiceCard;
