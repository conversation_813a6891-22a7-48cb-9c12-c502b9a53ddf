import PageLayout from "@/components/layout/PageLayout";
import CartCard from "../components/CartCard";
import ShipmentAddress from "../components/ShipmentAddress";
import CouponSection from "../components/CouponSection";
import OrderSummary from "../components/OrderSummary";
import { useQuery } from "@tanstack/react-query";
import { fetchCartItemsApi } from "../api";
import { useEffect, useState } from "react";
import CartItemSkeleton from "../components/CartItemSkelton";
import { Link } from "react-router-dom";
import { CartItem, CartItemResponse } from "../type";
import { useCartStore } from "@/store/cartStore";
function CartPage() {
  const { setCart } = useCartStore();
  const [hasAddress, setHasAddress] = useState<boolean>(false);
  const updateHasAddress = (address: boolean) => {
    setHasAddress(address);
  };

  const {
    data: carts,
    isPending: loading,
    isSuccess,
    refetch,
  } = useQuery({
    queryKey: ["cart"],
    queryFn: fetchCartItemsApi,
  });

  let subTotal = 0,
    shippingFee = 0,
    payableAmount = 0,
    discount = 0,
    promoCode = "",
    formatedCartItems = [];
  subTotal = carts?.data?.subTotal;
  shippingFee = carts?.data?.shippingFee;
  payableAmount = carts?.data?.payableAmount;
  discount = carts?.data?.discount;
  promoCode = carts?.data?.promoCode;

  formatedCartItems = carts?.data?.cart.map((item: CartItemResponse) => {
    return {
      _id: item?._id,
      image: item?.products?.images[0],
      title: item?.products?.title,
      price: item?.price,
      currentQuantity: item?.quantity,
      productId: item?.products?._id,
    };
  });
  useEffect(() => {
    setCart(formatedCartItems);
  }, [isSuccess]);

  return (
    <>
      <PageLayout>
        <div className="w-11/12 py-8 mx-auto">
          <h1 className="mb-6 text-lg font-semibold">My cart</h1>
          {!loading && carts?.data?.cart.length === 0 ? (
            <div className="flex flex-col justify-center md:flex-row gap-x-2">
              <h1 className="text-lg text-center">No items in your cart.</h1>
              <Link to="/shop" className="text-lg text-center text-orange-1">
                Explore products
              </Link>
            </div>
          ) : (
            <div className="grid grid-cols-12 gap-y-5 md:gap-y-0 md:gap-x-10">
              <div className="col-span-12 md:col-span-8 ">
                {loading
                  ? [1, 2, 3].map((_, i) => <CartItemSkeleton key={i} />)
                  : formatedCartItems.map((item: CartItem) => {
                      return (
                        <CartCard
                          key={item?._id}
                          _id={item?._id}
                          image={item?.image}
                          title={item?.title}
                          price={item?.price}
                          currentQuantity={item?.currentQuantity}
                          productId={item?.productId}
                          handleRefetch={refetch}
                        />
                      );
                    })}
              </div>
              <div className="flex flex-col col-span-12 md:col-span-4 gap-y-3">
                <ShipmentAddress updateHasAddress={updateHasAddress} />
                <CouponSection promoCode={promoCode} handleRefetch={refetch} />
                <OrderSummary
                  discount={discount}
                  payableAmount={payableAmount}
                  shippingFee={shippingFee}
                  subTotal={subTotal}
                  hasAddress={hasAddress}
                />
              </div>
            </div>
          )}
        </div>
      </PageLayout>
    </>
  );
}

export default CartPage;
