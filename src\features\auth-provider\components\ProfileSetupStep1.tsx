import { useState } from "react";
import { useForm } from "react-hook-form";
import { joiResolver } from "@hookform/resolvers/joi";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { setupProfileSchema } from "../validation";
import { showToast } from "@/lib/toast";
import FileUpload from "@/assets/file-upload.png";
import { ImageCropper } from "../../../components/ui/ImageCropper";
import { useMutation } from "@tanstack/react-query";
import { setupProfileApi } from "../api";
import { useAuthStore } from "@/store/authStore";
import { useNavigate } from "react-router-dom";

// Define allowed file types
const providerAllowedFileTypes = {
  profilePicture: ["image/jpeg", "image/png", "image/jpg", "image/webp"],
  introductionVideo: ["video/mp4", "video/mpeg", "video/quicktime"],
  certification: [
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  ],
};

type Step1FormData = {
  firstName: string;
  lastName: string;
  phone: string;
  specialty: string;
  businessName: string;
  taxId: string;
  yearsOfExperience: string;
  serviceLocation: string[];
  introductoryVideo: FileList;
  certifications: FileList;
  refundPolicy: string;
  photo?: File;
};

type ProfileSetupStep1Props = {
  onNext: () => void;
};

type FileWithPreview = File & {
  preview: string;
};

function ProfileSetupStep1({ onNext }: ProfileSetupStep1Props) {
  // State for image cropper
  const [selectedFile, setSelectedFile] = useState<FileWithPreview | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);

  // State for file uploads
  const [selectedVideoName, setSelectedVideoName] = useState<string>("Introductory video");
  const [selectedCertName, setSelectedCertName] = useState<string>("Certifications");

  // State for service location radio selection
  const [selectedServiceLocation, setSelectedServiceLocation] = useState<string>("");

  const {
    register,
    handleSubmit,
    setValue,
    trigger,
    formState: { errors },
  } = useForm<Step1FormData>({
    resolver: joiResolver(setupProfileSchema),
  });

  // Handle file selection for profile photo
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0] as FileWithPreview;
      file.preview = URL.createObjectURL(file);
      setSelectedFile(file);
      setDialogOpen(true);
    }
  };

  // Handle cropped image
  const handleCroppedImage = (file: File) => {
    // Set the cropped image to the form
    setValue("photo", file);
    trigger("photo"); // Trigger validation
  };

  // Handle video file selection
  const handleVideoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedVideoName(file.name);
      setValue("introductoryVideo", e.target.files as FileList);
      trigger("introductoryVideo"); // Trigger validation
    } else {
      setSelectedVideoName("Introductory video");
      setValue("introductoryVideo", undefined as any);
      trigger("introductoryVideo"); // Trigger validation
    }
  };

  // Handle certification file selection
  const handleCertChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedCertName(file.name);
      setValue("certifications", e.target.files as FileList);
      trigger("certifications"); // Trigger validation
    } else {
      setSelectedCertName("Certifications");
      setValue("certifications", undefined as any);
      trigger("certifications"); // Trigger validation
    }
  };

  const navigate = useNavigate();
  const login = useAuthStore((state) => state.login);

  // Setup API mutation
  const { mutate, isPending } = useMutation({
    mutationFn: setupProfileApi,
    onSuccess: (response) => {
      // Check if the response has the expected format
      if (response.status === "OK" && response.data?.provider) {
        showToast("Basic details saved successfully", "success");

        // Store the provider ID and status flags in localStorage
        if (response.data.provider._id) {
          const providerId = response.data.provider._id;
          console.log("Storing provider ID in localStorage:", providerId);
          localStorage.setItem("providerId", providerId);

          // Set status flags for profile completion
          localStorage.setItem("hasAddedServices", "false");
          localStorage.setItem("hasSetAvailability", "false");
          localStorage.setItem("planChoosen", "false");
        } else {
          console.error("Provider ID not found in response:", response.data);
        }

        // If there's an access token in the response, store it
        if (response.data.accessToken) {
          login({ accessToken: response.data.accessToken, role: "provider" });
        }

        // Move to next step
        onNext();
      } else {
        showToast("Profile setup completed", "success");
        onNext();
      }
    },
    onError: (error) => {
      console.error("Profile setup error:", error);
      showToast("Something went wrong", "error");
    },
  });



  // Handle form submission
  const onSubmit = (data: Step1FormData) => {

    // Validate service location
    if (!selectedServiceLocation) {
      showToast("Please select a service location", "error");
      return;
    }

    // Validate required file uploads
    if (!data.photo) {
      showToast("Profile picture is required", "error");
      return;
    }

    if (!data.introductoryVideo?.[0]) {
      showToast("Introductory video is required", "error");
      return;
    }

    if (!data.certifications?.[0]) {
      showToast("Certifications are required", "error");
      return;
    }

    // Create service location array with single selection
    const serviceLocations = [selectedServiceLocation];

    // Convert to comma-separated string for backend (single value)
    const serviceLocationString = selectedServiceLocation;

    // Set the service location value
    setValue("serviceLocation", serviceLocations);

    // Get auth ID from localStorage
    const authId = localStorage.getItem("userAuthId") as string;
    if (!authId) {
      showToast("Authentication error. Please try again.", "error");
      navigate("/provider/register");
      return;
    }

    // Validate file types
    if (data.introductoryVideo?.[0]) {
      const videoFile = data.introductoryVideo[0];
      if (!providerAllowedFileTypes.introductionVideo.includes(videoFile.type)) {
        showToast(`Invalid video format. Allowed formats: ${providerAllowedFileTypes.introductionVideo.join(', ')}`, "error");
        return;
      }
    }

    if (data.certifications?.[0]) {
      const certFile = data.certifications[0];
      if (!providerAllowedFileTypes.certification.includes(certFile.type)) {
        showToast(`Invalid certification format. Allowed formats: PDF, DOC, DOCX`, "error");
        return;
      }
    }

    if (data.photo) {
      if (!providerAllowedFileTypes.profilePicture.includes(data.photo.type)) {
        showToast(`Invalid image format. Allowed formats: JPEG, PNG, JPG, WEBP`, "error");
        return;
      }
    }

    // Create FormData object for file uploads
    const formData = new FormData();
    formData.append("firstName", data.firstName);
    formData.append("lastName", data.lastName);
    formData.append("phone", data.phone);
    formData.append("specialty", data.specialty);
    formData.append("businessName", data.businessName);
    formData.append("taxId", data.taxId);

    // Change yearsOfExperience to experience as per API requirements
    formData.append("experience", data.yearsOfExperience);

    // Add service location as comma-separated string
    formData.append("serviceLocation", serviceLocationString);

    // Add files if they exist
    if (data.introductoryVideo?.[0]) {
      formData.append("introductionVideo", data.introductoryVideo[0]);
    }

    if (data.certifications?.[0]) {
      formData.append("certifications", data.certifications[0]);
    }

    formData.append("refundPolicy", data.refundPolicy);

    // Change photo to profilePicture as per API requirements
    if (data.photo) {
      formData.append("profilePicture", data.photo);
    }

    formData.append("authId", authId);

    // Call the API
    mutate(formData);
  };

  return (
    <div className="w-full">
      {/* Photo Upload Section */}
      <div className="flex flex-col items-center mb-6">
        {selectedFile ? (
          <ImageCropper
            dialogOpen={dialogOpen}
            setDialogOpen={setDialogOpen}
            selectedFile={selectedFile}
            setSelectedFile={setSelectedFile}
            onImageCropped={handleCroppedImage}
          />
        ) : (
          <div
            className="w-36 h-36 rounded-full bg-red-50 flex items-center justify-center cursor-pointer border-2 border-dashed border-red-200"
            onClick={() => document.getElementById('photo-upload')?.click()}
          >
            <div className="text-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mx-auto text-red-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              <span className="text-sm text-gray-500 mt-1">Upload photo</span>
            </div>
            <input
              id="photo-upload"
              type="file"
              accept={providerAllowedFileTypes.profilePicture.join(',')}
              className="hidden"
              onChange={handleFileSelect}
            />
          </div>
        )}
        {errors.photo && (
          <p className="text-sm text-left text-red-500 mt-2">{errors.photo.message}</p>
        )}
      </div>

      <form className="space-y-4" onSubmit={handleSubmit(onSubmit)}>
        {/* Name Fields */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Input
              placeholder="First name"
              {...register("firstName")}
              className="border-input-border focus:border-slate-300"
            />
            {errors.firstName && (
              <p className="text-sm text-left text-red-500">{errors.firstName.message}</p>
            )}
          </div>
          <div>
            <Input
              placeholder="Last name"
              {...register("lastName")}
              className="border-input-border focus:border-slate-300"
            />
            {errors.lastName && (
              <p className="text-sm text-left text-red-500">{errors.lastName.message}</p>
            )}
          </div>
        </div>

        {/* Phone Number */}
        <div>
          <Input
            placeholder="Phone number"
            {...register("phone")}
            className="border-input-border focus:border-slate-300"
          />
          {errors.phone && (
            <p className="text-sm text-left text-red-500">{errors.phone.message}</p>
          )}
        </div>

        {/* Specialty */}
        <div>
          <Input
            placeholder="Specialty"
            {...register("specialty")}
            className="border-input-border focus:border-slate-300"
          />
          {errors.specialty && (
            <p className="text-sm text-left text-red-500">{errors.specialty.message}</p>
          )}
        </div>

        {/* Business Name */}
        <div>
          <Input
            placeholder="Business name"
            {...register("businessName")}
            className="border-input-border focus:border-slate-300"
          />
          {errors.businessName && (
            <p className="text-sm text-left text-red-500">{errors.businessName.message}</p>
          )}
        </div>

        {/* Tax ID */}
        <div>
          <Input
            placeholder="Tax ID"
            {...register("taxId")}
            className="border-input-border focus:border-slate-300"
          />
          {errors.taxId && (
            <p className="text-sm text-left text-red-500">{errors.taxId.message}</p>
          )}
        </div>

        {/* Years of Experience */}
        <div>
          <Input
            type="number"
            placeholder="Years of experience"
            min="0"
            max="50"
            {...register("yearsOfExperience")}
            className="border-input-border focus:border-slate-300"
            onKeyDown={(e) => {
              // Only allow numbers, backspace, delete, tab, and arrow keys
              if (!/[0-9]/.test(e.key) && !['Backspace', 'Delete', 'Tab', 'ArrowLeft', 'ArrowRight'].includes(e.key)) {
                e.preventDefault();
              }
            }}
          />
          {errors.yearsOfExperience && (
            <p className="text-sm text-left text-red-500">{errors.yearsOfExperience.message}</p>
          )}
        </div>

        {/* Preferred Service Location */}
        <div>
          <p className="text-base text-left text-[#665F5D] mb-4">Preferred service location</p>
          <div className="grid grid-cols-2 space-x-6 mb-2">
            <div className="flex items-center space-x-2">
              <input
                type="radio"
                id="virtual"
                name="serviceLocation"
                value="virtual"
                checked={selectedServiceLocation === "virtual"}
                onChange={(e) => setSelectedServiceLocation(e.target.value)}
                className="w-4 h-4 text-orange-500 border-gray-300 focus:ring-orange-500"
              />
              <Label htmlFor="virtual">Virtual</Label>
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="radio"
                id="in-person"
                name="serviceLocation"
                value="in-person"
                checked={selectedServiceLocation === "in-person"}
                onChange={(e) => setSelectedServiceLocation(e.target.value)}
                className="w-4 h-4 text-orange-500 border-gray-300 focus:ring-orange-500"
              />
              <Label htmlFor="in-person">In person</Label>
            </div>
          </div>
        </div>

        {/* Introductory Video Upload */}
        <div>
          <label
            htmlFor="video-upload"
            className="flex items-center border border-input-border rounded-md overflow-hidden cursor-pointer"
          >
            <div className="flex-grow text-base">
              <p className="text-start mx-3 text-gray-500 truncate">
                {selectedVideoName}
              </p>
            </div>
            <div className="p-2 flex items-center justify-center">
              <img src={FileUpload} className="h-5 w-5 text-gray-500" alt="Upload" />
            </div>
            <Input
              id="video-upload"
              type="file"
              accept={providerAllowedFileTypes.introductionVideo.join(',')}
              onChange={handleVideoChange}
              className="hidden"
            />
          </label>
          {errors.introductoryVideo && (
            <p className="text-sm text-left text-red-500">{errors.introductoryVideo.message}</p>
          )}
        </div>

        {/* Certifications Upload */}
        <div>
          <label
            htmlFor="cert-upload"
            className="flex items-center border border-input-border rounded-md overflow-hidden cursor-pointer"
          >
            <div className="flex-grow text-base">
              <p className="text-start mx-3 text-gray-500 truncate">
                {selectedCertName}
              </p>
            </div>
            <div className="p-2 flex items-center justify-center">
              <img src={FileUpload} className="h-5 w-5 text-gray-500" alt="Upload" />
            </div>
            <Input
              id="cert-upload"
              type="file"
              accept={providerAllowedFileTypes.certification.join(',')}
              onChange={handleCertChange}
              className="hidden"
            />
          </label>
          {errors.certifications && (
            <p className="text-sm text-left text-red-500">{errors.certifications.message}</p>
          )}
        </div>

        {/* Refund Policy */}
        <div>
          <Textarea
            placeholder="Refund policy"
            {...register("refundPolicy")}
            className="border-input-border focus:border-slate-300 min-h-[100px]"
          />
          {errors.refundPolicy && (
            <p className="text-sm text-left text-red-500">{errors.refundPolicy.message}</p>
          )}
        </div>

        {/* Next Button */}
        <Button
          type="submit"
          className="w-full mt-6"
          disabled={isPending}
        >
          {isPending ? "Saving..." : "Next"}
        </Button>
      </form>
    </div>
  );
}

export default ProfileSetupStep1;
