import { useEffect, useState } from "react";
import RecentC<PERSON>Header from "./RecentChatHeader";
import { fetchCustomerChatListApi } from "../api";
import { useQuery } from "@tanstack/react-query";
import { Conversation, SelectedChat } from "../type";
import RecentChatHeaderSkelton from "./RecentChatHeaderSkelton";

function CustomerChatList({
  currentFocus,
  selectedChatId,
  search,
  onChatSelect,
  handleHideMessageSection,
}: {
  currentFocus: "seller" | "provider";
  selectedChatId: string | null;
  search: string;
  onChatSelect: (data: SelectedChat) => void;
  handleHideMessageSection: () => void;
}) {
  const [currentPage, setCurrentPage] = useState(1);
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  const [focus, setFocus] = useState<"seller" | "provider">("seller");
  const handleChatSelect = (chat: Conversation) => {
    onChatSelect({
      conversationId: chat._id,
      name: chat.recipient.companyName ?? chat.recipient.firstName ?? "Unknown",
      description:
        chat.recipient.speciality ?? chat.recipient.category ?? "Unknown",
      profilePicture: chat.recipient.profilePicture?.[0],
      receiverId: chat.recipient._id,
      role: focus,
    });
    handleHideMessageSection();
  };
  const { data, isPending: loading } = useQuery({
    queryKey: ["customer-chat-list", focus, search],
    queryFn: () =>
      fetchCustomerChatListApi({ page: currentPage, category: focus, search }),
  });
  useEffect(() => {
    if (currentFocus) {
      setFocus(currentFocus);
    }
  }, [currentFocus]);
  return (
    <div className="w-full ">
      <div className="flex">
        <h1
          onClick={() => setFocus("seller")}
          className={`w-1/2 text-center cursor-pointer ${focus === "seller" ? "font-semibold	border-b-4 border-orange-1" : ""} `}
        >
          Sellers
        </h1>
        <h1
          onClick={() => setFocus("provider")}
          className={`w-1/2 text-center cursor-pointer ${focus === "provider" ? "font-semibold	border-b-4 border-orange-1" : ""} `}
        >
          Providers
        </h1>
      </div>
      <div className="w-full overflow-y-scroll max-h-[45vh] md:max-h-[50vh] scrollbar-hide ">
        {loading ? (
          <div className="space-y-3">
            {[1, 2].map((_, index) => (
              <RecentChatHeaderSkelton key={index} />
            ))}
          </div>
        ) : (
          data?.conversations.map((chat, index) => (
            <div
              className={`${
                [chat._id, chat.recipient._id].includes(
                  selectedChatId as string
                )
                  ? "bg-tints-40"
                  : ""
              }`}
              key={index}
              onClick={() => handleChatSelect(chat)}
            >
              <RecentChatHeader
                image={chat.recipient.profilePicture?.[0]?.url}
                name={
                  chat.recipient.firstName ||
                  chat.recipient.companyName ||
                  "Unknown"
                }
                lastMessage={chat.lastMessage.content}
                unreadCount={chat.unreadCount}
              />
            </div>
          ))
        )}
      </div>
    </div>
  );
}

export default CustomerChatList;
