import { useState } from "react";
import RecentChatHeader from "./RecentChatHeader";

function CustomerChatList({
  selectedChatId,
  onChatSelect,
  handleHideMessageSection,
}: {
  selectedChatId: string | null;
  onChatSelect: (chatId: string) => void;
  handleHideMessageSection: () => void;
}) {
  const [focus, setFocus] = useState<"seller" | "provider">("seller");
  const handleChatSelect = (chatId: string) => {
    onChatSelect(chatId);
    handleHideMessageSection();
  };
  return (
    <div className="w-full ">
      <div className="flex">
        <h1
          onClick={() => setFocus("seller")}
          className={`w-1/2 text-center cursor-pointer ${focus === "seller" ? "font-semibold	border-b-4 border-orange-1" : ""} `}
        >
          Sellers
        </h1>
        <h1
          onClick={() => setFocus("provider")}
          className={`w-1/2 text-center cursor-pointer ${focus === "provider" ? "font-semibold	border-b-4 border-orange-1" : ""} `}
        >
          Providers
        </h1>
      </div>
      <div className="w-full overflow-y-scroll max-h-[45vh] md:max-h-[50vh] scrollbar-hide ">
        {[1, 2, 3, 4, 5].map((_, index) => (
          <div
            className={`${selectedChatId === index.toString() ? "bg-tints-40" : ""}`}
            key={index}
            onClick={() => handleChatSelect(index.toString())}
          >
            <RecentChatHeader
              image="https://nurture-ye-s3.s3.us-east-1.amazonaws.com/public/nurture-images/1747977307621.png"
              name="Seller"
              lastMessage="Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.
        
"
              unreadCount={1}
            />
          </div>
        ))}
      </div>
    </div>
  );
}

export default CustomerChatList;
