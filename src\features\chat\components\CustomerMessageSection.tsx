import { useEffect, useRef, useState } from "react";
import { Input } from "@/components/ui/input";
import { timeFormatter } from "@/lib/utils";
import GoBackIcon from "@/assets/go-back.svg";
import SendIcon from "@/assets/send.svg";
import Buttonwithicon from "@/components/ui/buttonwithicon";
import EmptyConversation from "./EmptyConversation";

function CustomerMessageSection({
  name,
  description,
  image,
  handleHideMessageSection,
}: {
  name: string;
  description: string;
  image?: string | undefined;
  handleHideMessageSection: () => void;
}) {
  const user = localStorage.getItem("nurtureUser");
  const [message, setMessage] = useState("");
  const lastMessageRef = useRef(null);
  useEffect(() => {
    // Scroll to the last message
    if (lastMessageRef.current) {
      (lastMessageRef.current as HTMLElement)?.scrollIntoView({
        behavior: "smooth",
      });
    }
  }, []);
  const conversations = [
    {
      id: 3,
      message: "Hello",
      sender: "seller",
      timestamp: "2025-06-02T10:25:47.123Z",
    },
  ]

  const handleSendMessage = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!message.trim()) return;
    // Send message
    setMessage("");
  };

  // Check if conversation is empty
  if (conversations.length === 0) {
    return (
      <EmptyConversation handleHideMessageSection={handleHideMessageSection} />
    );
  }

  return (
    <div className="flex flex-col w-full">
      <div className="flex p-4 border-b gap-x-3 md:gap-0 border-nueutral-40">
        <img
          onClick={handleHideMessageSection}
          src={GoBackIcon}
          alt="go back"
          className="w-4 h-4 my-auto cursor-pointer md:hidden"
        />
        <div className={`${image ? "flex gap-x-2" : ""} `}>
          {image && (
            <div className="flex-shrink-0 w-12 h-12 overflow-hidden rounded-full">
              <img
                src={image}
                alt={"profile"}
                className="object-cover w-full h-full"
              />
            </div>
          )}
          <div>
            <h1>{name}</h1>
            <h5 className="text-sm text-neutral-300">{description}</h5>
          </div>
        </div>
      </div>
      <div
        className={`w-full flex flex-col grow overflow-y-scroll min-h-[65vh] max-h-[65vh] scrollbar-hide p-5 `}
      >
        {conversations.map((conversation, index) => (
          <div
            ref={index === conversations.length - 1 ? lastMessageRef : null}
            className={` my-3 p-3 max-w-[80%] md:max-w-[49%] border ${
              conversation.sender === user
                ? "bg-coral text-white self-end rounded-tr-none rounded-xl"
                : "self-start rounded-tl-none rounded-xl border-neutral-50"
            }`}
          >
            <p>{conversation.message}</p>
            <p
              className={`${conversation.sender === user ? "text-tints-60" : "text-neutral-300"} text-sm text-end mt-1`}
            >
              {timeFormatter(conversation.timestamp)}
            </p>
          </div>
        ))}
      </div>
      {/* Input */}
      <form
        onSubmit={handleSendMessage}
        className="flex p-4 border-t gap-x-2 border-neutral-40"
      >
        <Input
          type="text"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder="Type message here..."
          className="w-full p-2 border rounded-md grow border-neutral-40"
        />
        <Buttonwithicon
          disabled={message.trim().length === 0}
          icon={SendIcon}
          variant="button"
        />
      </form>
    </div>
  );
}

export default CustomerMessageSection;
