import { useEffect, useRef, useState, useCallback, useMemo } from "react";
import { Input } from "@/components/ui/input";
import { timeFormatter } from "@/lib/utils";
import GoBackIcon from "@/assets/go-back.svg";
import SendIcon from "@/assets/send.svg";
import Buttonwithicon from "@/components/ui/buttonwithicon";
import EmptyConversation from "./EmptyConversation";
import {
  fetchConversationMessagesApi,
  fetchConversationsApi,
  sendMessageApi,
} from "../api";
import MessageSkeleton from "./MessageSkelton";
import { useMutation, useQuery } from "@tanstack/react-query";

function CustomerMessageSection({
  name,
  description,
  image,
  handleHideMessageSection,
  receiverId,
  conversationId,
}: {
  name: string;
  description: string;
  image?: string | undefined;
  receiverId: string;
  conversationId?: string;
  handleHideMessageSection: () => void;
}) {
  const user = localStorage.getItem("nurtureUser") as string;
  const [message, setMessage] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMoreMessages, setHasMoreMessages] = useState(true);
  const [shouldAutoScroll, setShouldAutoScroll] = useState(true);

  const lastMessageRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const isLoadingMoreRef = useRef(false);

  const handlePageChange = useCallback((page: number) => {
    console.log(page, "page");
    setCurrentPage(page);
  }, []);
  const {
    data: conversationsWithConversationId,
    isLoading: conversationLoading,
    isFetching: conversationFetching,
  } = useQuery({
    queryKey: ["conversation-messages", conversationId, currentPage],
    queryFn: () =>
      fetchConversationMessagesApi(conversationId as string, currentPage),
    enabled: !!conversationId,
    staleTime: 30000, // 30 seconds
    refetchOnWindowFocus: false,
  });

  const {
    data: conversationsWithReceiverId,
    isLoading: conversationwithReceiverIdLoading,
    isFetching: conversationWithReceiverIdFetching,
  } = useQuery({
    queryKey: ["conversation-messages", receiverId, currentPage],
    queryFn: () => fetchConversationsApi(receiverId as string, currentPage),
    enabled: !conversationId && !!receiverId,
    staleTime: 30000, // 30 seconds
    refetchOnWindowFocus: false,
  });

  const conversations = useMemo(() =>
    conversationsWithConversationId ?? conversationsWithReceiverId,
    [conversationsWithConversationId, conversationsWithReceiverId]
  );

  const isLoading = conversationLoading || conversationwithReceiverIdLoading;
  const isFetching = conversationFetching || conversationWithReceiverIdFetching;

  const { mutate, isPending: sendMessageLoading } = useMutation({
    mutationFn: sendMessageApi,
    onSuccess: () => {},
    onError: () => {},
  });
  // Infinite scroll logic
  const handleScroll = useCallback(() => {
    const container = messagesContainerRef.current;
    if (!container || isLoadingMoreRef.current || !hasMoreMessages) return;

    // Check if user scrolled to the top (for loading older messages)
    if (container.scrollTop === 0 && conversations?.chats?.length) {
      isLoadingMoreRef.current = true;
      setIsLoadingMore(true);

      // Store current scroll height to maintain position after loading
      const previousScrollHeight = container.scrollHeight;

      handlePageChange(currentPage + 1);

      // Reset loading state after a delay to prevent rapid calls
      setTimeout(() => {
        isLoadingMoreRef.current = false;
        setIsLoadingMore(false);

        // Maintain scroll position after loading new messages
        if (container) {
          const newScrollHeight = container.scrollHeight;
          container.scrollTop = newScrollHeight - previousScrollHeight;
        }
      }, 1000);
    }
  }, [currentPage, hasMoreMessages, conversations?.chats?.length, handlePageChange]);

  // Auto-scroll to bottom logic
  const scrollToBottom = useCallback(() => {
    if (lastMessageRef.current && shouldAutoScroll) {
      lastMessageRef.current.scrollIntoView({
        behavior: "smooth",
        block: "end",
      });
    }
  }, [shouldAutoScroll]);

  // Handle scroll events
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    container.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      container.removeEventListener('scroll', handleScroll);
    };
  }, [handleScroll]);

  // Auto-scroll to latest message when new messages arrive
  useEffect(() => {
    if (conversations?.chats?.length && currentPage === 1) {
      // Only auto-scroll for the first page (latest messages)
      scrollToBottom();
    }
  }, [conversations?.chats?.length, currentPage, scrollToBottom]);

  // Update hasMoreMessages based on API response
  useEffect(() => {
    if (conversations?.chats) {
      // Assuming API returns fewer messages when there are no more
      const messagesPerPage = 20; // Adjust based on your API
      setHasMoreMessages(conversations.chats.length >= messagesPerPage * currentPage);
    }
  }, [conversations?.chats, currentPage]);

  // Handle user scroll behavior detection
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    const handleUserScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = container;
      const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;

      // Enable auto-scroll when user is near bottom
      setShouldAutoScroll(isNearBottom);
    };

    container.addEventListener('scroll', handleUserScroll, { passive: true });

    return () => {
      container.removeEventListener('scroll', handleUserScroll);
    };
  }, []);

  const handleSendMessage = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!message.trim() || sendMessageLoading) return;

    // Enable auto-scroll when sending a message
    setShouldAutoScroll(true);

    let mutationPayload = { receiverId, content: message };
    if (conversationId) {
      mutationPayload = {
        ...mutationPayload,
        ...{ conversationId: conversationId },
      };
    }
    mutate(mutationPayload);
    setMessage("");
  };

  // Check if conversation is empty
  if (conversationId && !conversations?.chats?.length) {
    return (
      <EmptyConversation handleHideMessageSection={handleHideMessageSection} />
    );
  }
  return (
    <div className="flex flex-col w-full h-full">
      {/* Header */}
      <div className="flex flex-shrink-0 p-4 border-b gap-x-3 md:gap-0 border-nueutral-40">
        <img
          onClick={handleHideMessageSection}
          src={GoBackIcon}
          alt="go back"
          className="w-4 h-4 my-auto cursor-pointer md:hidden"
        />
        <div className={`${image ? "flex gap-x-2" : ""} `}>
          {image && (
            <div className="flex-shrink-0 w-12 h-12 overflow-hidden rounded-full">
              <img
                src={image}
                alt={"profile"}
                className="object-cover w-full h-full"
              />
            </div>
          )}
          <div>
            <h1>{name}</h1>
            <h5 className="text-sm text-neutral-300">{description}</h5>
          </div>
        </div>
      </div>

      {/* Messages Container with Custom Infinite Scroll */}
      <div className="flex-1 min-h-0 overflow-hidden">
        {/* Loading indicator for older messages */}
        {isLoadingMore && (
          <div className="flex justify-center py-2 border-b border-neutral-40">
            <div className="flex items-center gap-2 text-sm text-neutral-500">
              <div className="w-4 h-4 border-2 rounded-full border-coral border-t-transparent animate-spin"></div>
              Loading older messages...
            </div>
          </div>
        )}

        <div
          ref={messagesContainerRef}
          className="flex flex-col w-full p-5 overflow-y-auto min-h-[65vh] max-h-[65vh] scrollbar-hide"
        >
          {isLoading
            ? [1, 2].map((num, index) => (
                <MessageSkeleton key={index} isSender={num % 2 === 0} />
              ))
            : conversations?.chats?.map((conversation, index) => {
                return (
                  <div
                    key={conversation?._id}
                    ref={
                      index === conversations?.chats?.length - 1
                        ? lastMessageRef
                        : null
                    }
                    className={`my-3 p-3 max-w-[80%] md:max-w-[49%] border ${
                      conversation?.sentBy == user
                        ? "bg-coral text-white self-end rounded-tr-none rounded-xl"
                        : "self-start rounded-tl-none rounded-xl border-neutral-50"
                    }`}
                  >
                    <p className="break-words">{conversation?.content}</p>
                    <p
                      className={`${
                        conversation?.sentBy == user
                          ? "text-tints-60"
                          : "text-neutral-300"
                      } text-sm text-end mt-1`}
                    >
                      {timeFormatter(conversation?.createdAt)}
                    </p>
                  </div>
                );
              })}

          {/* End of messages indicator */}
          {!hasMoreMessages && (conversations?.chats?.length || 0) > 0 && (
            <div className="flex justify-center py-4">
              <div className="text-sm text-neutral-400">
                No more messages
              </div>
            </div>
          )}
        </div>
      </div>
      {/* Input */}
      <form
        onSubmit={handleSendMessage}
        className="flex flex-shrink-0 p-4 border-t gap-x-2 border-neutral-40"
      >
        <Input
          type="text"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder="Type message here..."
          className="w-full p-2 border rounded-md grow border-neutral-40"
        />
        <Buttonwithicon
          disabled={message.trim().length === 0}
          icon={SendIcon}
          variant="button"
        />
      </form>
    </div>
  );
}

export default CustomerMessageSection;
