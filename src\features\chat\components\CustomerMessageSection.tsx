import { useEffect, useRef, useState } from "react";
import { Input } from "@/components/ui/input";
import { timeFormatter } from "@/lib/utils";
import GoBackIcon from "@/assets/go-back.svg";
import SendIcon from "@/assets/send.svg";
import Buttonwithicon from "@/components/ui/buttonwithicon";
import EmptyConversation from "./EmptyConversation";
import {
  fetchConversationMessagesApi,
  fetchConversationsApi,
  sendMessageApi,
} from "../api";
import MessageSkeleton from "./MessageSkelton";
import { useMutation, useQuery } from "@tanstack/react-query";
import InfiniteScroll from "react-infinite-scroll-component";

function CustomerMessageSection({
  name,
  description,
  image,
  handleHideMessageSection,
  receiverId,
  conversationId,
}: {
  name: string;
  description: string;
  image?: string | undefined;
  receiverId: string;
  conversationId?: string;
  handleHideMessageSection: () => void;
}) {
  const user = localStorage.getItem("nurtureUser") as string;
  const [message, setMessage] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const handlePageChange = (page: number) => {
    console.log(page, "page");
    setCurrentPage(page);
  };
  const lastMessageRef = useRef(null);
  const {
    data: conversationsWithConversationId,
    isLoading: conversationLoading,
  } = useQuery({
    queryKey: ["conversation-messages", conversationId, currentPage],
    queryFn: () =>
      fetchConversationMessagesApi(conversationId as string, currentPage),
    enabled: !!conversationId,
  });
  const {
    data: conversationsWithReceiverId,
    isLoading: conversationwithReceiverIdLoading,
  } = useQuery({
    queryKey: ["conversation-messages", receiverId, currentPage],
    queryFn: () => fetchConversationsApi(receiverId as string, currentPage),
    enabled: !conversationId && !!receiverId,
  });

  const conversations =
    conversationsWithConversationId ?? conversationsWithReceiverId;

  const { mutate, isPending: sendMessageLoading } = useMutation({
    mutationFn: sendMessageApi,
    onSuccess: () => {},
    onError: () => {},
  });
  const handleSendMessage = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!message.trim() || sendMessageLoading) return;
    let mutationPayload = { receiverId, content: message };
    if (conversationId) {
      mutationPayload = {
        ...mutationPayload,
        ...{ conversationId: conversationId },
      };
    }
    mutate(mutationPayload);
    setMessage("");
  };
  useEffect(() => {
    // Scroll to the last message
    if (lastMessageRef.current) {
      (lastMessageRef.current as HTMLElement)?.scrollIntoView({
        behavior: "smooth",
      });
    }
  }, [conversations]);

  // Check if conversation is empty
  if (conversationId && !conversations?.chats?.length) {
    return (
      <EmptyConversation handleHideMessageSection={handleHideMessageSection} />
    );
  }
  return (
    <div className="flex flex-col w-full">
      <div className="flex p-4 border-b gap-x-3 md:gap-0 border-nueutral-40">
        <img
          onClick={handleHideMessageSection}
          src={GoBackIcon}
          alt="go back"
          className="w-4 h-4 my-auto cursor-pointer md:hidden"
        />
        <div className={`${image ? "flex gap-x-2" : ""} `}>
          {image && (
            <div className="flex-shrink-0 w-12 h-12 overflow-hidden rounded-full">
              <img
                src={image}
                alt={"profile"}
                className="object-cover w-full h-full"
              />
            </div>
          )}
          <div>
            <h1>{name}</h1>
            <h5 className="text-sm text-neutral-300">{description}</h5>
          </div>
        </div>
      </div>

      {/* <InfiniteScroll
        loader={<h4>Loading...</h4>}
        dataLength={conversations?.chats?.length || 0}
        hasMore={!!conversations?.chats.length}
        next={() => handlePageChange(currentPage + 1)}
        height={100}
        endMessage={<h4>No more messages</h4>}
        className=""
      > */}
        <div
          className={`flex flex-col w-full p-5 grow overflow-y-scroll min-h-[65vh] max-h-[65vh] scrollbar-hide`}
        >
          {conversationwithReceiverIdLoading || conversationLoading
            ? [1, 2].map((num, index) => (
                <MessageSkeleton key={index} isSender={num % 2 === 0} />
              ))
            : conversations?.chats.map((conversation, index) => {
                return (
                  <div
                    key={conversation?._id}
                    ref={
                      index === conversations?.chats?.length - 1
                        ? lastMessageRef
                        : null
                    }
                    className={` my-3 p-3 max-w-[80%] md:max-w-[49%] border ${
                      conversation?.sentBy == user
                        ? "bg-coral text-white self-end rounded-tr-none rounded-xl"
                        : "self-start rounded-tl-none rounded-xl border-neutral-50"
                    }`}
                  >
                    <p>{conversation?.content}</p>
                    <p
                      className={`${conversation?.sentBy == user ? "text-tints-60" : "text-neutral-300"} text-sm text-end mt-1`}
                    >
                      {timeFormatter(conversation?.createdAt)}
                    </p>
                  </div>
                );
              })}
        </div>
      {/* </InfiniteScroll> */}
      {/* Input */}
      <form
        onSubmit={handleSendMessage}
        className="flex p-4 border-t gap-x-2 border-neutral-40"
      >
        <Input
          type="text"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder="Type message here..."
          className="w-full p-2 border rounded-md grow border-neutral-40"
        />
        <Buttonwithicon
          disabled={message.trim().length === 0}
          icon={SendIcon}
          variant="button"
        />
      </form>
    </div>
  );
}

export default CustomerMessageSection;
