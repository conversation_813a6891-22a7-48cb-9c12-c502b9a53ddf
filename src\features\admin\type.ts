export type LoginPayload = {
  email: string;
  password: string;
};

export type Seller = {
  _id: string;
  companyName: string;
  authId: { email: string };
  taxId: string;
  category: string;
  phoneNumber: string;
  refundPolicy: string;
  video: number;
  isVerified?: boolean;
};

export type Provider = {
  _id: string;
  authId: {
    email: string;
  };
  role: string;
  specialty: string;
  taxId: string;
  phone: string;
  firstName: string;
  businessName: string;
  serviceLocation: string[];
  certifications: {
    url: string;
    key: string;
    _id: string;
  }[];
  profilePicture: {
    image: string;
    key: string;
  }[];
  introductionVideo: {
    video: string;
    key: string;
  };
};

export type SellerApiPayload = {
  type: string;
  page: number;
};
export type ApproveSellerApiPayload = {
  sellerId: string;
  isApproved: boolean;
};
export type ApproveProviderApiPayload = {
  providerId: string;
  isApproved: boolean;
};

export type SellerApiResponse = {
  seller: {
    introductionVideo: string;
    authId: {
      email: string;
    };
    isValid: boolean;
    ratingSum: number;
    totalReview: number;
    totalRating: number;
    _id: string;
    role: "seller";
    companyName: string;
    category: string;
    taxId: string;
    refundPolicy: string;
    isDeleted: boolean;
    isApproved: boolean;
    createdAt: string; // ISO date string
    phone: string;
    id: string;
  };
  productCount: number;
};

export type Product = {
  _id: string;
  title: string;
  description: string;
  quantity: number;
  price: number;
  totalReview: number;
  images: {
    image: string;
    key: string;
    _id: string;
  }[];
  seller: {
    companyName: string;
    category: string;
  };
};

export type SellerProductsResponse = {
  products: Product[];
  totalPages: number;
};

export type ProductApiResponse = {
  title: string;
  description: string;
  quantity: number;
  price: number;
  stockStatus: string;
  images: {
    image: string;
    key: string;
    _id: string;
  }[];
};
