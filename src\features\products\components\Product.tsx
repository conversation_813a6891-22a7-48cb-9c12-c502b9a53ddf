import { useEffect, useState } from "react";
import RatingIcon from "@/assets/rating-star.png";
import { Button } from "@/components/ui/button";
import { Link, useNavigate } from "react-router-dom";
import PlusWhiteIcon from "@/assets/white-plusicon.png";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { useMutation, useQuery } from "@tanstack/react-query";
import { addToCartApi, getProductDetailsApi } from "../api";
import RefundModal from "./RefundModal";
import { showToast } from "@/lib/toast";
import { useAuthStore } from "@/store/authStore";
import { AxiosError } from "axios";
import GoBack from "@/components/ui/go-back";
import { Skeleton } from "@/components/ui/skeleton";

function Product({ productId }: { productId: string }) {
  const nurtureUser = localStorage.getItem("nurtureUser");
  const { user, setAccesstoken } = useAuthStore((state) => state);
  const navigate = useNavigate();
  const [quantity, setQuantity] = useState(1);
  const [selectedImage, setSelectedImage] = useState(0);
  const [isOpen, setIsOpen] = useState(false);
  const onClose = () => setIsOpen(false);

  const {
    data,
    isSuccess,
    isPending: loading,
  } = useQuery({
    queryKey: ["product", productId],
    queryFn: () => getProductDetailsApi(productId),
  });

  const productDetails = data?.data?.product;
  const decreaseQuantity = () => {
    if (quantity > 1) {
      setQuantity(quantity - 1);
    }
  };

  const increaseQuantity = () => {
    if (quantity >= 10) return;
    setQuantity(quantity + 1);
  };

  const { mutate, isPending } = useMutation({
    mutationFn: addToCartApi,
    onSuccess: () => {
      showToast("Product added to cart successfully!", "success");
      setQuantity(1);
      navigate("/cart");
    },
    onError: (error: AxiosError<Array<{ message?: string }>>) => {
      if (error.response?.status === 400) {
        showToast(
          error.response?.data[0]?.message || "failed to add to cart",
          "error"
        );
        return;
      }
      showToast("Something went wrong", "error");
    },
  });
  const handleAddToCart = () => {
    if (!nurtureUser) {
      showToast("Please login to continue", "error");
      navigate("/login");
      return;
    }
    mutate({ productId, count: quantity, type: "inc" });
  };

  useEffect(() => {
    if (!user?.accessToken && nurtureUser === "customer") {
      setAccesstoken();
    }
  }, []);
  return (
    <>
      <div className="px-4 ">
        {/* Back button */}
        <GoBack />

        <div className="flex flex-col gap-6 md:flex-row md:gap-x-16">
          <div className="flex flex-col md:flex-row md:gap-x-3 md:w-1/2">
            <div className="flex order-last pb-2 overflow-x-auto md:flex-col md:order-first md:gap-y-3 scrollbar-hide">
              {isSuccess &&
                productDetails.images.map(
                  (image: { image: string; key: string }, index: number) => (
                    <div
                      key={index}
                      className={`flex-shrink-0 mx-auto p-2 w-14 h-14 md:w-16 md:h-16 border-2  cursor-pointer ${
                        selectedImage === index
                          ? "border-orange-1"
                          : "border-gray-200"
                      }`}
                      onClick={() => setSelectedImage(index)}
                    >
                      <img
                        src={image.image}
                        alt={`thumbnail ${index + 1}`}
                        className="object-cover w-full h-full mx-auto"
                      />
                    </div>
                  )
                )}
            </div>
            {/* Main image */}
            {loading ? (
              <Skeleton className="w-full h-full aspect-square grow max-h-[400px]" />
            ) : (
              <div className="aspect-square grow max-h-[400px]  w-full md:w-3/5 border-2 px-5 border-neutral-40 overflow-hidden mb-4">
                <img
                  src={
                    isSuccess
                      ? productDetails.images[selectedImage].image
                      : null
                  }
                  alt={"product"}
                  className="object-contain w-full h-full"
                />
              </div>
            )}
          </div>

          {/* Product Details - Right side on desktop, bottom on mobile */}
          <div className="md:w-1/2">
            <h1 className="mb-2 text-xl font-medium md:text-3xl">
              {productDetails?.title || ""}
            </h1>

            <div className="flex my-4 font-bold gap-x-1 md:my-6">
              <p className="my-auto text-2xl">$</p>
              <span className="text-2xl md:text-3xl">
                {productDetails?.price || 0}
              </span>
            </div>
            <div className="flex flex-wrap items-center mb-4 text-sm text-neutral-300 gap-x-2 gap-y-1 md:text-base">
              <div className="flex items-center">
                <img src={RatingIcon} className="w-5 h-5 my-auto" />
                <span className="ml-1 font-medium">
                  {productDetails?.ratingSum}
                </span>
              </div>
              <span className="text-neutral-300">|</span>
              <span className="text-neutral-300">
                {productDetails?.totalReview} reviews
              </span>
              <span className="text-neutral-300">|</span>
              <span>{productDetails?.stockStatus}</span>
            </div>

            <div className="mb-4 text-sm md:text-base">
              <Link
                to={`/product/seller/${productDetails?.sellerId?._id}`}
                className="underline"
              >
                {productDetails?.sellerId?.companyName}
              </Link>
              <span className="mx-2">•</span>
              <span className="text-neutral-300">Please read </span>
              <span
                onClick={() => setIsOpen(true)}
                className="my-auto underline cursor-pointer text-neutral-300"
              >
                return & refund policy
              </span>
            </div>

            {/* Quantity Selector */}
            <div className="flex items-center mb-6">
              <button
                onClick={decreaseQuantity}
                className="flex items-center justify-center border border-r-0 border-gray-300 w-9 h-9 md:w-10 md:h-10 rounded-l-md"
                aria-label="Decrease quantity"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M5 12h14" />
                </svg>
              </button>
              <input
                type="text"
                value={quantity}
                readOnly
                aria-label="Quantity"
                className="w-10 text-center border-t border-b border-gray-300 md:w-12 h-9 md:h-10"
              />
              <button
                onClick={increaseQuantity}
                disabled={quantity >= 10}
                className="flex items-center justify-center border border-l-0 border-gray-300 w-9 h-9 md:w-10 md:h-10 rounded-r-md"
                aria-label="Increase quantity"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M12 5v14M5 12h14" />
                </svg>
              </button>
            </div>

            {/* Add to Cart Button */}
            <Button
              onClick={handleAddToCart}
              disabled={isPending}
              className="flex items-center justify-center w-full gap-2 py-3 mb-8"
              variant="default"
            >
              <span>{isPending ? "Adding..." : "Add to cart"}</span>
              <img
                src={PlusWhiteIcon}
                className="text-white"
                alt="add to cart"
              />
            </Button>
            <div className="">
              <Accordion className="border-gray-200" type="single" collapsible>
                <AccordionItem value="item-1">
                  <AccordionTrigger className="font-bold">
                    Description
                  </AccordionTrigger>
                  <AccordionContent className="text-base">
                    {productDetails?.description}
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </div>
          </div>
        </div>
      </div>
      <RefundModal
        isOpen={isOpen}
        onClose={onClose}
        refundPolicy={productDetails?.sellerId?.refundPolicy}
      />
    </>
  );
}

export default Product;
