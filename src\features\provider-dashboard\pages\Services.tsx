import { useState, useEffect } from "react";
import Layout from "@/components/layout/ProviderLayout";
import ServiceCard from "../components/ServiceCard";
import ServiceDetailsDialog, {
  ServiceDetails,
} from "../components/ServiceDetailsDialog";
import { Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  getProviderServicesApi,
  ProviderService,
  createServiceApi,
  ServiceCreationPayload,
} from "../api";
import { showToast } from "@/lib/toast";
import { useMutation } from "@tanstack/react-query";

function ServicesPage() {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [services, setServices] = useState<(ServiceDetails & { id: string })[]>(
    []
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage] = useState(1);
  const [, setTotalPages] = useState(1);

  // Transform API service data to UI format
  const transformApiServiceToUI = (
    apiService: ProviderService
  ): ServiceDetails & { id: string } => {
    return {
      id: apiService._id,
      serviceName: apiService.title,
      duration: `${apiService.duration} hour${apiService.duration > 1 ? "s" : ""}`,
      price: apiService.price.toString(),
      description: apiService.description,
      highlights: [],
    };
  };

  // Fetch provider services
  const fetchServices = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get provider ID from localStorage
      const providerId = localStorage.getItem("providerId");
      if (!providerId) {
        throw new Error("Provider ID not found. Please log in again.");
      }

      const response = await getProviderServicesApi(providerId, currentPage);
      const transformedServices = response.data.services.map(
        transformApiServiceToUI
      );

      setServices(transformedServices);
      setTotalPages(response.data.totalPages);
    } catch (err) {
      const errorMessage =
        err instanceof Error
          ? err.message
          : "Failed to fetch services. Please try again.";
      setError(errorMessage);
      showToast(errorMessage, "error");
      console.error("Error fetching services:", err);
    } finally {
      setLoading(false);
    }
  };

  // Fetch services on component mount and page change
  useEffect(() => {
    fetchServices();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentPage]);

  // Service creation mutation
  const { mutate: createService, isPending: isCreatingService } = useMutation({
    mutationFn: createServiceApi,
    onSuccess: (response) => {
      if (response.status === "OK" && response.data?.services) {
        console.log(
          "Service created successfully:",
          response.data.services._id
        );
        showToast("Service created successfully", "success");
        // Refresh the services list
        fetchServices();
      }
    },
    onError: (error) => {
      console.error("Service creation error:", error);
      showToast("Failed to create service. Please try again.", "error");
    },
  });

  const handleAddService = async (serviceData: ServiceDetails) => {
    try {
      // Get provider ID from localStorage
      const providerId = localStorage.getItem("providerId");
      if (!providerId) {
        showToast("Provider ID not found. Please log in again.", "error");
        return;
      }

      // Prepare service payload for API
      const servicePayload: ServiceCreationPayload = {
        title: serviceData.serviceName,
        providerId: providerId,
        duration: parseFloat(serviceData.duration), // Convert string to number
        description: serviceData.description,
        price: parseFloat(serviceData.price), // Convert string to number
        highlights: serviceData.highlights.map((h) => h.text),
      };

      console.log("Creating service:", servicePayload);

      // Create service via API
      createService(servicePayload);
    } catch (error) {
      console.error("Error preparing service data:", error);
      showToast("Failed to create service. Please check your input.", "error");
    }
  };

  return (
    <Layout showFooter={true}>
      <div className="w-full">
        {/* Header section */}
        <div className="flex items-center justify-between mb-5">
          <h1 className="text-xl font-semibold">Services Provided</h1>
          <Button
            onClick={() => setDialogOpen(true)}
            className="flex items-center gap-2 bg-orange-1 hover:bg-orange-1/90"
            disabled={isCreatingService}
          >
            {isCreatingService ? "Creating Service..." : "Add new service"}
            {!isCreatingService && <Plus className="w-4 h-4" />}
          </Button>
        </div>

        {/* Service cards grid */}
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-lg">Loading services...</div>
          </div>
        ) : error ? (
          <div className="flex flex-col items-center justify-center h-64 gap-4">
            <div className="text-lg text-center text-red-500">{error}</div>
            <Button onClick={fetchServices} variant="outline" className="mt-2">
              Try Again
            </Button>
          </div>
        ) : services.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-64 gap-4">
            <div className="text-lg text-gray-500">No services found</div>
            <p className="text-center text-gray-400">
              Start by adding your first service to showcase your expertise
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            {services.map((service, index) => (
              <ServiceCard key={service.id || index} service={service} />
            ))}
          </div>
        )}

        {/* Service Details Dialog */}
        <ServiceDetailsDialog
          open={dialogOpen}
          onOpenChange={setDialogOpen}
          onSubmit={handleAddService}
        />
      </div>
    </Layout>
  );
}

export default ServicesPage;
