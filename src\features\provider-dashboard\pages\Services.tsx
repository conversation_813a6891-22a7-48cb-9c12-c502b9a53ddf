import { useState } from "react";
import Layout from "../components/Layout";
import ServiceCard from "../components/ServiceCard";
import ServiceDetailsDialog, { ServiceDetails } from "../components/ServiceDetailsDialog";
import { Plus } from "lucide-react";
import { Button } from "@/components/ui/button";

function ServicesPage() {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [services, setServices] = useState<ServiceDetails[]>([
    {
      serviceName: "Lactation consulting",
      duration: "1 hour",
      price: "80",
      description: "Lorem ipsum dolor sit amet consectetur. Elit pulvinar sed lorem et justo sagittis. Habitasse posuere tellus gravida dui. Pellentesque vel non bibendum et velit sit. Pharetra felis odio blandit dapibus feugiat nul feugiat nul feugitu...",
      highlights: []
    },
    {
      serviceName: "Lactation consulting",
      duration: "1 hour",
      price: "80",
      description: "Lorem ipsum dolor sit amet consectetur. Elit pulvinar sed lorem et justo sagittis. Habitasse posuere tellus gravida dui. Pellentesque vel non bibendum et velit sit. Pharetra felis odio blandit dapibus feugiat nul feugiat nul feugitu...",
      highlights: []
    }
  ]);

  const handleAddService = (serviceData: ServiceDetails) => {
    setServices([...services, serviceData]);
  };

  return (
    <Layout showFooter={true}>
      <div className="w-full">
        {/* Header section */}
        <div className="flex justify-between items-center mb-5">
          <h1 className="text-xl font-semibold">Services Provided</h1>
          <Button
            onClick={() => setDialogOpen(true)}
            className="flex items-center gap-2 bg-orange-1 hover:bg-orange-1/90"
          >
            Add new service <Plus className="h-4 w-4" />
          </Button>
        </div>

        {/* Service cards grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {services.map((service, index) => (
            <ServiceCard
              key={index}
              service={service}
            />
          ))}
        </div>

        {/* Service Details Dialog */}
        <ServiceDetailsDialog
          open={dialogOpen}
          onOpenChange={setDialogOpen}
          onSubmit={handleAddService}
        />
      </div>
    </Layout>
  );
}

export default ServicesPage;
