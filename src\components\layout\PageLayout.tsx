import React, { useState } from "react";
import Header from "./Header";
import Footer from "./Footer";

function PageLayout({
  children,
  isShopOrServicePage = false,
}: {
  children: React.ReactNode;
  isShopOrServicePage?: boolean;
}) {
  const [open, setOpen] = useState(false);
  return (
    <div className="flex flex-col min-h-screen">
      <Header
        isShopOrServicePage={isShopOrServicePage}
        open={open}
        setOpen={setOpen}
      />
      {!open && (
        <>
          <div className="my-2 grow">{children}</div>
          <Footer />
        </>
      )}
    </div>
  );
}

export default PageLayout;
