import React, { useState } from "react";
import Header from "./Header";
import Footer from "./Footer";

function PageLayout({ children }: { children: React.ReactNode }) {
  const [open, setOpen] = useState(false);
  return (
    <div className="flex flex-col min-h-screen">
      <Header open={open} setOpen={setOpen} />
      {!open && (
        <>
          <div className="grow my-2">{children}</div>
          <Footer />
        </>
      )}
    </div>
  );
}

export default PageLayout;
