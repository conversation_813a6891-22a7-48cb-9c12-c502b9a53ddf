import NurtureLogo from "@/assets/nurture-logo.png";

import { IoEllipsisVertical } from "react-icons/io5";
import { Link } from "react-router-dom";
import { useState } from "react";
import LogoutModal from "@/components/ui/logout-modal";

function Header() {
  const [toggle, setToggle] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  const onClose = () => {
    setIsOpen(false);
  };

  const onOpen = () => {
    setIsOpen(true);
  };

  const handleToggle = (e: React.MouseEvent) => {
    e.stopPropagation();
    setToggle(!toggle);
  };

  const handleClose = () => {
    if (toggle) {
      setToggle(false);
    }
  };

  return (
    <>
      <div
        className="flex justify-between w-11/12 mx-auto"
        onClick={handleClose}
      >
        <div>
          <img src={NurtureLogo} alt="nurture" className="mx-auto h-7" />
        </div>
        <div className="flex gap-x-5">
          <IoEllipsisVertical
            className="mx-auto h-9"
            onClick={(e) => handleToggle(e)}
          />
          {toggle && (
            <div
              className="absolute right-3 top-16 bg-white shadow-xl border-gray-50 rounded-md p-4"
            >
              <ul className="flex flex-col gap-y-4">
                <Link to="/provider/profile" className="text-sm">
                  My account
                </Link>
                <li onClick={onOpen} className="text-sm">
                  Logout
                </li>
              </ul>
            </div>
          )}
        </div>
      </div>
      <LogoutModal isOpen={isOpen} onClose={onClose} />
    </>
  );
}

export default Header;
