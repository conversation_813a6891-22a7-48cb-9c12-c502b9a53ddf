import { Modal } from "@/components/ui/modal";
import { fetchSellerApi } from "../api";
import { useQuery } from "@tanstack/react-query";
import CloseIcon from "@/assets/close-icon.svg";
import NextIcon from "@/assets/next.svg";
import { Skeleton } from "@/components/ui/skeleton";

function SellerDetailsModal({
  isOpen,
  onClose,
  onOpenProducts,
  selectedSellerId,
}: {
  isOpen: boolean;
  onClose: () => void;
  onOpenProducts: () => void;
  selectedSellerId: string;
}) {
  const {
    data,
    isPending: loading,
    isSuccess,
  } = useQuery({
    queryKey: ["seller-details", selectedSellerId],
    queryFn: () => fetchSellerApi(selectedSellerId),
    enabled: !!selectedSellerId && isOpen,
  });

  const handleOpenProducts = () => {
    onOpenProducts();
    onClose();
  };

  const DetailRow = ({
    label,
    value,
  }: {
    label: string;
    value: string | number;
  }) => (
    <div className="flex items-start justify-between py-3 border-b border-gray-100 ">
      <span className="w-1/3 text-sm font-medium text-gray-600">{label}</span>
      <span className="w-2/3 text-sm text-right text-gray-900">{value}</span>
    </div>
  );

  const RefundPolicySection = ({ policy }: { policy: string }) => (
    <div className="">
      <h3 className="text-base font-semibold text-gray-900 ">Refund Policy</h3>
      <div className="p-4 rounded-lg bg-gray-50">
        <p className="text-sm leading-relaxed text-gray-700">{policy}</p>
      </div>
    </div>
  );

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      className="md:min-w-[600px] max-h-[90vh] overflow-y-auto"
    >
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <h1 className="text-xl font-semibold text-gray-900">All details</h1>
          <img
            onClick={onClose}
            className="transition-opacity cursor-pointer hover:opacity-70"
            src={CloseIcon}
            alt="close"
          />
        </div>

        {loading ? (
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <Skeleton className="w-16 h-16 rounded-full" />
              <div className="space-y-2">
                <Skeleton className="w-48 h-6" />
                <Skeleton className="w-64 h-4" />
              </div>
            </div>
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex justify-between">
                <Skeleton className="w-24 h-4" />
                <Skeleton className="w-32 h-4" />
              </div>
            ))}
          </div>
        ) : isSuccess && data ? (
          <>
            {/* Profile Section */}
            <div className="flex items-center space-x-4 border-b border-gray-200">
              <div>
                <h2 className="text-lg font-semibold text-gray-900">
                  {data.seller?.companyName || "N/A"}
                </h2>
                <p className="text-sm text-gray-600">
                  {data.seller?.authId?.email || "N/A"}
                </p>
              </div>
            </div>

            {/* Services Section */}
            <div
              className="flex items-center justify-between my-auto transition-colors border-gray-100 cursor-pointer border-y"
              onClick={handleOpenProducts}
            >
              <div className="flex items-center space-x-3">
                <span className="font-medium text-gray-900">
                  {data.productCount > 1
                    ? `${data.productCount} products`
                    : `${data.productCount} product`}{" "}
                  listed
                </span>
              </div>
              <img src={NextIcon} alt="view services" className="" />
            </div>

            {/* Details Section */}
            <div className="space-y-1">
              <DetailRow
                label="Phone number"
                value={data.seller?.phone || "N/A"}
              />
              <DetailRow
                label="Business name"
                value={data.seller?.companyName || "N/A"}
              />
              <DetailRow label="Tax ID" value={data.seller?.taxId || "N/A"} />

              <DetailRow
                label="Introduction video"
                value={
                  data.seller?.introductionVideo
                    ? "Introvideo.mp4"
                    : "No video uploaded"
                }
              />
            </div>

            {/* Refund Policy */}
            {data.seller?.refundPolicy && (
              <RefundPolicySection policy={data.seller.refundPolicy} />
            )}
          </>
        ) : (
          <div className="py-8 text-center">
            <p className="text-gray-500">Failed to load seller details</p>
          </div>
        )}
      </div>
    </Modal>
  );
}

export default SellerDetailsModal;
