import { Modal } from "@/components/ui/modal";
import { fetchSellerApi } from "../api";
import { useQuery } from "@tanstack/react-query";
import CloseIcon from "@/assets/close-icon.svg";
import NextIcon from "@/assets/next.svg";

function SellerDetailsModal({
  isOpen,
  onClose,
  onOpenProducts,
  selectedSellerId,
}: {
  isOpen: boolean;
  onClose: () => void;
  onOpenProducts: () => void;
  selectedSellerId: string;
}) {
  const {
    data,
    isPending: loading,
    isSuccess,
  } = useQuery({
    queryKey: ["seller-details"],
    queryFn: () => fetchSellerApi(selectedSellerId),
  });
  console.log("seller details", data);
  const handleopneProducts = () => {
    onOpenProducts();
    onClose();
  };
  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <div>
        <div className="flex justify-between">
          <h1 className="text-lg font-semibold">All Details</h1>
          <img
            onClick={onClose}
            className="cursor-pointer"
            src={CloseIcon}
            alt="close"
          />
        </div>
        <div className="w-11/12 mx-auto mt-8">
          <div>
            <h1>{data?.seller.companyName}</h1>
             <h5>{data?.seller.}</h5>
          </div>
        </div>
      </div>
    </Modal>
  );
}

export default SellerDetailsModal;
