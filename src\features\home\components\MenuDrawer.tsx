import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { MenuDrawerProps } from "../types";
import Headerlogo from "@/assets/header-logo.png";
import CloseLogo from "@/assets/close-icon.png";
import Shop<PERSON>ogo from "@/assets/shop-icon.png";
import Service<PERSON>ogo from "@/assets/service-icon.png";
import BlogsLogo from "@/assets/blogs-icon.png";
import Cha<PERSON><PERSON> from "@/assets/chat.png";
import ProfileLogo from "@/assets/profile-icon.png";

function MenuDrawer({ open, onClose }: MenuDrawerProps) {
  const isLoggedIn = localStorage.getItem("nurtureUser");

  if (!open) return null;
  const NavigationList = [
    {
      name: "Shop",
      icon: ShopLogo,
      href: "/shop",
    },
    {
      name: "Service",
      icon: ServiceLogo,
      href: "/services",
    },
    {
      name: "Blogs",
      icon: BlogsLogo,
      href: "/blogs",
    },
    {
      name: "Messages",
      icon: <PERSON><PERSON><PERSON>,
      href: "/chat",
    },
    {
      name: "Account",
      icon: ProfileLogo,
      href: "/account/basic-details",
      authorize: isLoggedIn,
    },
  ];
  return (
    <div className="min-h-screen md:hidden min-w-screen ">
      <div className="min-h-[90vh] flex flex-col  w-11/12 mx-auto">
        <div className="flex justify-between mt-5">
          <Link to={"/"}>
            <img src={Headerlogo} alt="Header Logo" className="h-9" />
          </Link>
          <img
            onClick={onClose}
            src={CloseLogo}
            alt="close"
            className="my-auto"
          />
        </div>
        <div className="mt-16 grow">
          {NavigationList.map((item) => {
            return item.authorize === undefined || item.authorize ? (
              <Link
                to={item.href}
                className="flex mb-8 gap-x-4 "
                key={item.name}
              >
                <div className="flex gap-x-3">
                  <img src={item.icon} alt={item.name} className="my-auto" />{" "}
                  <span className="text-lg ">{item.name}</span>
                </div>
              </Link>
            ) : null;
          })}
        </div>
        {!isLoggedIn && (
          <Link to={"/register"} className="my-5">
            <Button className="w-full py-5">Sign Up</Button>
          </Link>
        )}
      </div>
    </div>
  );
}

export default MenuDrawer;
