export type BreadcrumbItemType = {
  name: string;
  href: string;
};

export type Orderstatus =
  | "order confirmed"
  | "item shipped"
  | "item delivered"
  | "item cancelled"
  | "exchange requested"
  | "request approved"
  | "request rejected"
  | "return rejected"
  | "exchange rejected"
  | "shipment pending"
  | "delivery pending"
  | "return requested"
  | "exchange approved"
  | "return approved"
  | "item returned";

export type OrderItems = {
  _id: string;
  product: {
    images: { image: string; key: string }[];
    title: string;
    sellerId?: string;
    _id: string;
  };
  status: { status: string; completed: boolean; date: string };
  hasRequestedReview: boolean;
  reviewAdded: boolean;
};

export type BasicDetailsData = {
  firstName: string;
  lastName: string;
  phone: string;
  birthStatus: "currently pregnant" | "loss history" | "postpartum";
  preferredLanguage: string;
};

export type ShippingAddressData = {
  address: string;
  city: string;
  state: string;
  zipCode: string;
};
export type returnOrExchangeApiPayload = {
  orderId: string;
  type: "return" | "exchange";
  reason: string[];
  images: File[];
};

export type CarePlanCardItem = {
  title: string;
  description: string;
  frequency: string;
  category: string;
};