import * as React from "react";
import * as SliderPrimitive from "@radix-ui/react-slider";
import { cn } from "@/lib/utils";

const CustomSlider = React.forwardRef<
  React.ElementRef<typeof SliderPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root>
>(({ className, ...props }, ref) => (
  <SliderPrimitive.Root
    ref={ref}
    className={cn(
      "relative flex w-full touch-none select-none items-center",
      className
    )}
    {...props}
  >
    <SliderPrimitive.Track className="relative w-full h-2 overflow-hidden rounded-full grow bg-neutral-40">
      <SliderPrimitive.Range className="absolute h-full bg-orange-1" />
    </SliderPrimitive.Track>
    <SliderPrimitive.Thumb className="block w-6 h-6 transition-colors bg-white border-2 rounded-full border-orange-1 ring-offset-background focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50" />
    <SliderPrimitive.Thumb className="block w-6 h-6 transition-colors bg-white border-2 rounded-full border-orange-1 ring-offset-background focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50" />
  </SliderPrimitive.Root>
));

CustomSlider.displayName = SliderPrimitive.Root.displayName;

export default CustomSlider ;
